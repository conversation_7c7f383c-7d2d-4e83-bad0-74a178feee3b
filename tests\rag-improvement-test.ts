/**
 * Test script to validate RAG improvements
 * Tests the specific failing queries mentioned in the issue
 */

import { semanticChunkText, chunkText } from '../utils/textChunking';
import { findSimilarVectors } from '../utils/vector';
import { smartEnhanceQuery, enhanceQuery } from '../utils/queryEnhancement';
import { embedContent, embedChunks } from '../services/geminiService';
import { readFileSync } from 'fs';
import { join } from 'path';

interface TestQuery {
  query: string;
  expectedKeywords: string[];
  description: string;
}

const TEST_QUERIES: TestQuery[] = [
  {
    query: "what is the support email address",
    expectedKeywords: ["<EMAIL>", "email", "contact"],
    description: "Should find the support email address"
  },
  {
    query: "refund policy",
    expectedKeywords: ["30-day", "money-back", "guarantee", "refund"],
    description: "Should find refund policy information"
  },
  {
    query: "what is payment method",
    expectedKeywords: ["Paddle", "SSLCOMMERZ", "PayPal", "credit", "debit"],
    description: "Should find payment method information"
  },
  {
    query: "what is the service about",
    expectedKeywords: ["eComEasyAI", "AI-powered", "product descriptions", "e-commerce"],
    description: "Should find service description (this was working before)"
  }
];

async function runRAGTest() {
  console.log('🧪 Starting RAG Improvement Test\n');
  
  try {
    // Load the test document
    const docPath = join(__dirname, '../00_Docs/RAG_eComEasy.md');
    const documentContent = readFileSync(docPath, 'utf-8');
    console.log(`📄 Loaded document: ${documentContent.length} characters\n`);

    // Test old vs new chunking
    console.log('🔍 Comparing Chunking Strategies:');
    await testChunkingStrategies(documentContent);

    // Test query enhancement
    console.log('\n🔍 Testing Query Enhancement:');
    testQueryEnhancement();

    // Test full RAG pipeline
    console.log('\n🔍 Testing Full RAG Pipeline:');
    await testFullRAGPipeline(documentContent);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

async function testChunkingStrategies(content: string) {
  // Old chunking strategy
  const oldChunks = chunkText(content, 1000, 200);
  console.log(`📊 Old chunking: ${oldChunks.length} chunks`);
  console.log(`   Average chunk size: ${Math.round(oldChunks.reduce((sum, chunk) => sum + chunk.length, 0) / oldChunks.length)} chars`);

  // New semantic chunking
  const newChunks = semanticChunkText(content, {
    maxChunkSize: 500,
    minChunkSize: 100,
    overlapSize: 100,
    preserveStructure: true
  });
  console.log(`📊 New chunking: ${newChunks.length} chunks`);
  console.log(`   Average chunk size: ${Math.round(newChunks.reduce((sum, chunk) => sum + chunk.content.length, 0) / newChunks.length)} chars`);

  // Show sample chunks containing support email
  console.log('\n📋 Sample chunks containing "<EMAIL>":');
  
  const oldEmailChunks = oldChunks.filter(chunk => chunk.includes('<EMAIL>'));
  console.log(`   Old strategy found ${oldEmailChunks.length} chunks with email`);
  if (oldEmailChunks.length > 0) {
    console.log(`   Sample old chunk (${oldEmailChunks[0].length} chars): "${oldEmailChunks[0].substring(0, 200)}..."`);
  }

  const newEmailChunks = newChunks.filter(chunk => chunk.content.includes('<EMAIL>'));
  console.log(`   New strategy found ${newEmailChunks.length} chunks with email`);
  if (newEmailChunks.length > 0) {
    console.log(`   Sample new chunk (${newEmailChunks[0].content.length} chars): "${newEmailChunks[0].content.substring(0, 200)}..."`);
  }
}

function testQueryEnhancement() {
  for (const testQuery of TEST_QUERIES) {
    console.log(`\n🔍 Query: "${testQuery.query}"`);
    
    const enhanced = enhanceQuery(testQuery.query, {
      includeSynonyms: true,
      includeContext: true,
      includeVariations: true
    });
    
    console.log(`   Enhanced: "${enhanced.enhanced}"`);
    console.log(`   Metadata: ${JSON.stringify(enhanced.metadata)}`);
    
    const smart = smartEnhanceQuery(testQuery.query);
    console.log(`   Smart enhanced: "${smart}"`);
  }
}

async function testFullRAGPipeline(content: string) {
  console.log('🚀 Testing full RAG pipeline with improved components...\n');
  
  // Create chunks using new strategy
  const chunks = semanticChunkText(content, {
    maxChunkSize: 500,
    minChunkSize: 100,
    overlapSize: 100,
    preserveStructure: true
  });
  
  console.log(`📦 Created ${chunks.length} semantic chunks`);
  
  // Create embeddings (this requires API key)
  try {
    const chunkTexts = chunks.map(chunk => chunk.content);
    const vectors = await embedChunks(chunkTexts);
    console.log(`🔢 Generated ${vectors.length} embeddings`);
    
    // Test each query
    for (const testQuery of TEST_QUERIES) {
      console.log(`\n🔍 Testing: "${testQuery.query}"`);
      console.log(`   Expected to find: ${testQuery.expectedKeywords.join(', ')}`);
      
      // Enhance query
      const enhancedQuery = smartEnhanceQuery(testQuery.query);
      console.log(`   Enhanced query: "${enhancedQuery}"`);
      
      // Get query embedding
      const queryEmbedding = await embedContent(enhancedQuery);
      
      // Search for similar vectors
      const searchResult = findSimilarVectors(queryEmbedding, vectors, {
        minSimilarity: 0.25,
        maxResults: 3,
        includeScores: true,
        debug: true
      });
      
      console.log(`   Found ${searchResult.results.length} relevant chunks`);
      
      if (searchResult.results.length > 0) {
        console.log(`   Top similarity scores: ${searchResult.scores?.map(s => (s * 100).toFixed(1) + '%').join(', ')}`);
        
        // Check if expected keywords are found
        const foundKeywords = testQuery.expectedKeywords.filter(keyword => 
          searchResult.results.some(result => 
            result.text.toLowerCase().includes(keyword.toLowerCase())
          )
        );
        
        console.log(`   ✅ Found keywords: ${foundKeywords.join(', ')}`);
        console.log(`   ❌ Missing keywords: ${testQuery.expectedKeywords.filter(k => !foundKeywords.includes(k)).join(', ')}`);
        
        // Show top result snippet
        const topResult = searchResult.results[0];
        const snippet = topResult.text.substring(0, 200) + (topResult.text.length > 200 ? '...' : '');
        console.log(`   📄 Top result snippet: "${snippet}"`);
      } else {
        console.log(`   ❌ No relevant chunks found - this query would fail`);
      }
    }
    
  } catch (error) {
    console.log('⚠️  Skipping embedding tests (API key required)');
    console.log('   To run full tests, ensure VITE_GEMINI_API_KEY is set');
    console.log(`   Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Export for use in other tests
export { runRAGTest, TEST_QUERIES };

// Run if called directly
if (require.main === module) {
  runRAGTest();
}
