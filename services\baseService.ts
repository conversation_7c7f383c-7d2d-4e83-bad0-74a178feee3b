/**
 * Base service class with common functionality for all services
 */

export interface ServiceConfig {
  retryAttempts?: number;
  retryDelay?: number;
  timeout?: number;
  enableLogging?: boolean;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
}

export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ServiceError;
  metadata?: {
    requestId: string;
    timestamp: Date;
    duration: number;
    retryCount?: number;
  };
}

export interface ServiceError {
  code: string;
  message: string;
  details?: any;
  retryable: boolean;
  category: 'network' | 'validation' | 'permission' | 'processing' | 'unknown';
}

export interface RequestMetadata {
  requestId: string;
  timestamp: Date;
  method: string;
  params?: any;
  userId?: string;
  projectId?: string;
}

export interface LogEntry {
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  metadata: RequestMetadata;
  error?: Error;
  duration?: number;
}

export abstract class BaseService {
  protected config: Required<ServiceConfig>;
  protected serviceName: string;

  constructor(serviceName: string, config: ServiceConfig = {}) {
    this.serviceName = serviceName;
    this.config = {
      retryAttempts: 3,
      retryDelay: 1000,
      timeout: 30000,
      enableLogging: true,
      logLevel: 'info',
      ...config,
    };
  }

  /**
   * Generate a unique request ID
   */
  protected generateRequestId(): string {
    return `${this.serviceName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Log service operations
   */
  protected log(entry: Omit<LogEntry, 'metadata'> & { metadata?: Partial<RequestMetadata> }): void {
    if (!this.config.enableLogging) return;

    const logLevels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = logLevels.indexOf(this.config.logLevel);
    const entryLevelIndex = logLevels.indexOf(entry.level);

    if (entryLevelIndex < currentLevelIndex) return;

    const fullEntry: LogEntry = {
      ...entry,
      metadata: {
        requestId: this.generateRequestId(),
        timestamp: new Date(),
        method: 'unknown',
        ...entry.metadata,
      },
    };

    // In a real application, you might send this to a logging service
    console.log(`[${fullEntry.level.toUpperCase()}] ${this.serviceName}:`, {
      message: fullEntry.message,
      metadata: fullEntry.metadata,
      error: fullEntry.error?.message,
      duration: fullEntry.duration,
    });
  }

  /**
   * Execute a service operation with retry logic and logging
   */
  protected async executeWithRetry<T>(
    operation: () => Promise<T>,
    metadata: Partial<RequestMetadata> = {},
    customRetryAttempts?: number
  ): Promise<ServiceResponse<T>> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();
    const maxAttempts = customRetryAttempts ?? this.config.retryAttempts;
    
    const fullMetadata: RequestMetadata = {
      requestId,
      timestamp: new Date(),
      method: 'unknown',
      ...metadata,
    };

    this.log({
      level: 'info',
      message: `Starting operation: ${fullMetadata.method}`,
      metadata: fullMetadata,
    });

    let lastError: Error | null = null;
    let retryCount = 0;

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const result = await Promise.race([
          operation(),
          this.createTimeoutPromise<T>(),
        ]);

        const duration = Date.now() - startTime;

        this.log({
          level: 'info',
          message: `Operation completed successfully: ${fullMetadata.method}`,
          metadata: fullMetadata,
          duration,
        });

        return {
          success: true,
          data: result,
          metadata: {
            requestId,
            timestamp: fullMetadata.timestamp,
            duration,
            retryCount,
          },
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        retryCount = attempt;

        const serviceError = this.categorizeError(lastError);

        this.log({
          level: attempt === maxAttempts - 1 ? 'error' : 'warn',
          message: `Operation failed (attempt ${attempt + 1}/${maxAttempts}): ${fullMetadata.method}`,
          metadata: fullMetadata,
          error: lastError,
        });

        // Don't retry if error is not retryable or this is the last attempt
        if (!serviceError.retryable || attempt === maxAttempts - 1) {
          break;
        }

        // Wait before retrying with exponential backoff
        const delay = this.config.retryDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    const duration = Date.now() - startTime;
    const serviceError = this.categorizeError(lastError!);

    return {
      success: false,
      error: serviceError,
      metadata: {
        requestId,
        timestamp: fullMetadata.timestamp,
        duration,
        retryCount,
      },
    };
  }

  /**
   * Create a timeout promise
   */
  private createTimeoutPromise<T>(): Promise<T> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Operation timed out after ${this.config.timeout}ms`));
      }, this.config.timeout);
    });
  }

  /**
   * Categorize errors for better handling
   */
  protected categorizeError(error: Error): ServiceError {
    const message = error.message.toLowerCase();

    // Network errors
    if (message.includes('fetch') || message.includes('network') || message.includes('connection')) {
      return {
        code: 'NETWORK_ERROR',
        message: error.message,
        retryable: true,
        category: 'network',
      };
    }

    // Timeout errors
    if (message.includes('timeout') || message.includes('timed out')) {
      return {
        code: 'TIMEOUT_ERROR',
        message: error.message,
        retryable: true,
        category: 'network',
      };
    }

    // Validation errors
    if (message.includes('invalid') || message.includes('validation') || message.includes('required')) {
      return {
        code: 'VALIDATION_ERROR',
        message: error.message,
        retryable: false,
        category: 'validation',
      };
    }

    // Permission errors
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
      return {
        code: 'PERMISSION_ERROR',
        message: error.message,
        retryable: false,
        category: 'permission',
      };
    }

    // Processing errors
    if (message.includes('processing') || message.includes('failed to process')) {
      return {
        code: 'PROCESSING_ERROR',
        message: error.message,
        retryable: true,
        category: 'processing',
      };
    }

    // Default to unknown retryable error
    return {
      code: 'UNKNOWN_ERROR',
      message: error.message,
      retryable: true,
      category: 'unknown',
    };
  }

  /**
   * Validate input parameters
   */
  protected validateInput(input: any, schema: ValidationSchema): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const rule of schema.rules) {
      try {
        const result = rule.validate(input);
        if (result === false) {
          errors.push(rule.message || `Validation failed for rule: ${rule.name}`);
        } else if (typeof result === 'string') {
          if (rule.severity === 'error') {
            errors.push(result);
          } else {
            warnings.push(result);
          }
        }
      } catch (error) {
        errors.push(`Validation error in rule ${rule.name}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Get service health status
   */
  public async getHealthStatus(): Promise<ServiceResponse<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    checks: Array<{
      name: string;
      status: 'pass' | 'fail';
      message?: string;
      duration?: number;
    }>;
  }>> {
    return this.executeWithRetry(
      async () => {
        const checks = await this.performHealthChecks();
        const failedChecks = checks.filter(check => check.status === 'fail');
        
        let status: 'healthy' | 'degraded' | 'unhealthy';
        if (failedChecks.length === 0) {
          status = 'healthy';
        } else if (failedChecks.length < checks.length / 2) {
          status = 'degraded';
        } else {
          status = 'unhealthy';
        }

        return { status, checks };
      },
      { method: 'getHealthStatus' },
      1 // Only try once for health checks
    );
  }

  /**
   * Perform service-specific health checks
   */
  protected abstract performHealthChecks(): Promise<Array<{
    name: string;
    status: 'pass' | 'fail';
    message?: string;
    duration?: number;
  }>>;
}

export interface ValidationRule {
  name: string;
  validate: (value: any) => boolean | string;
  message?: string;
  severity?: 'error' | 'warning';
}

export interface ValidationSchema {
  rules: ValidationRule[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Common validation rules
export const CommonValidationRules = {
  required: (fieldName: string): ValidationRule => ({
    name: `required_${fieldName}`,
    validate: (value: any) => value != null && value !== '',
    message: `${fieldName} is required`,
    severity: 'error',
  }),

  minLength: (fieldName: string, minLength: number): ValidationRule => ({
    name: `minLength_${fieldName}`,
    validate: (value: string) => !value || value.length >= minLength,
    message: `${fieldName} must be at least ${minLength} characters long`,
    severity: 'error',
  }),

  maxLength: (fieldName: string, maxLength: number): ValidationRule => ({
    name: `maxLength_${fieldName}`,
    validate: (value: string) => !value || value.length <= maxLength,
    message: `${fieldName} must be no more than ${maxLength} characters long`,
    severity: 'error',
  }),

  url: (fieldName: string): ValidationRule => ({
    name: `url_${fieldName}`,
    validate: (value: string) => {
      if (!value) return true;
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    },
    message: `${fieldName} must be a valid URL`,
    severity: 'error',
  }),

  fileType: (fieldName: string, allowedTypes: string[]): ValidationRule => ({
    name: `fileType_${fieldName}`,
    validate: (file: File) => {
      if (!file) return true;
      return allowedTypes.some(type =>
        file.name.toLowerCase().endsWith(type.toLowerCase()) ||
        file.type.toLowerCase().includes(type.toLowerCase())
      );
    },
    message: `${fieldName} must be one of: ${allowedTypes.join(', ')}`,
    severity: 'error',
  }),

  fileSize: (fieldName: string, maxSizeBytes: number): ValidationRule => ({
    name: `fileSize_${fieldName}`,
    validate: (file: File) => {
      if (!file) return true;
      return file.size <= maxSizeBytes;
    },
    message: `${fieldName} must be smaller than ${(maxSizeBytes / 1024 / 1024).toFixed(1)}MB`,
    severity: 'error',
  }),
};
