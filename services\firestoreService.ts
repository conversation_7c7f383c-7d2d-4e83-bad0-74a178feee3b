import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  writeBatch,
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { User, Project, KnowledgeChunk, ProjectStatus, SourceType } from '../types';

// Collection names
const COLLECTIONS = {
  USERS: 'users',
  PROJECTS: 'projects',
  CHUNKS: 'chunks',
} as const;

// Helper function to convert Firestore timestamps to Date objects
const convertTimestamps = (data: any): any => {
  const converted = { ...data };
  Object.keys(converted).forEach(key => {
    if (converted[key] instanceof Timestamp) {
      converted[key] = converted[key].toDate();
    } else if (converted[key] && typeof converted[key] === 'object') {
      // Skip arrays (especially number arrays like embeddings) to prevent corruption
      if (Array.isArray(converted[key])) {
        // For arrays, only process if they contain objects that might have timestamps
        // Skip primitive arrays like number[] (embeddings) entirely
        const firstElement = converted[key][0];
        if (firstElement && typeof firstElement === 'object' && !Array.isArray(firstElement)) {
          // Only process arrays of objects, not primitive arrays
          converted[key] = converted[key].map((item: any) =>
            typeof item === 'object' ? convertTimestamps(item) : item
          );
        }
        // Otherwise leave the array unchanged (this preserves embedding arrays)
      } else {
        // Process non-array objects recursively
        converted[key] = convertTimestamps(converted[key]);
      }
    }
  });
  return converted;
};

// Helper function to sanitize data for Firestore (removes undefined values)
const sanitizeForFirestore = (data: any): any => {
  const sanitized: any = {};
  Object.keys(data).forEach(key => {
    const value = data[key];
    if (value !== undefined) {
      if (value && typeof value === 'object' && !(value instanceof Date) && !(value instanceof Timestamp)) {
        sanitized[key] = sanitizeForFirestore(value);
      } else {
        sanitized[key] = value;
      }
    }
    // Note: undefined values are simply omitted from the sanitized object
  });
  return sanitized;
};

// User operations
export const createUser = async (userId: string, userData: Omit<User, 'id'>): Promise<void> => {
  try {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    const userWithTimestamps = {
      ...userData,
      createdAt: Timestamp.fromDate(userData.createdAt),
      updatedAt: Timestamp.fromDate(userData.updatedAt),
    };

    // Sanitize data to remove undefined values before sending to Firestore
    const sanitizedData = sanitizeForFirestore(userWithTimestamps);
    await setDoc(userRef, sanitizedData);
  } catch (error: any) {
    console.error('Error creating user:', error);

    // Provide more specific error messages
    if (error?.code === 'permission-denied') {
      throw new Error('Missing or insufficient permissions to create user document. Please check Firestore security rules.');
    } else if (error?.code === 'unavailable') {
      throw new Error('Firestore service is temporarily unavailable. Please try again later.');
    } else if (error?.code === 'unauthenticated') {
      throw new Error('User is not authenticated. Please sign in again.');
    } else {
      throw new Error(`Failed to create user: ${error?.message || 'Unknown error'}`);
    }
  }
};

export const getUser = async (userId: string): Promise<User | null> => {
  try {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    const userSnap = await getDoc(userRef);

    if (userSnap.exists()) {
      const userData = userSnap.data();
      return convertTimestamps({ id: userSnap.id, ...userData }) as User;
    }
    return null;
  } catch (error: any) {
    console.error('Error getting user:', error);

    // Provide more specific error messages
    if (error?.code === 'permission-denied') {
      throw new Error('Missing or insufficient permissions to access user data. Please check Firestore security rules.');
    } else if (error?.code === 'unavailable') {
      throw new Error('Firestore service is temporarily unavailable. Please try again later.');
    } else if (error?.code === 'unauthenticated') {
      throw new Error('User is not authenticated. Please sign in again.');
    } else {
      throw new Error(`Failed to get user: ${error?.message || 'Unknown error'}`);
    }
  }
};

export const updateUser = async (userId: string, updates: Partial<Omit<User, 'id' | 'createdAt'>>): Promise<void> => {
  try {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.now(),
    };

    // Sanitize data to remove undefined values before sending to Firestore
    const sanitizedData = sanitizeForFirestore(updateData);
    await updateDoc(userRef, sanitizedData);
  } catch (error) {
    console.error('Error updating user:', error);
    throw new Error('Failed to update user');
  }
};

// Project operations
export const createProject = async (projectData: Omit<Project, 'id'>): Promise<string> => {
  try {
    const projectsRef = collection(db, COLLECTIONS.PROJECTS);
    const projectWithTimestamps = {
      ...projectData,
      createdAt: Timestamp.fromDate(projectData.createdAt),
      updatedAt: Timestamp.fromDate(projectData.updatedAt),
      knowledgeBaseStats: {
        ...projectData.knowledgeBaseStats,
        lastTrainingDate: projectData.knowledgeBaseStats.lastTrainingDate
          ? Timestamp.fromDate(projectData.knowledgeBaseStats.lastTrainingDate)
          : null,
      },
    };

    // Sanitize data to remove undefined values before sending to Firestore
    const sanitizedData = sanitizeForFirestore(projectWithTimestamps);
    const docRef = await addDoc(projectsRef, sanitizedData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating project:', error);
    throw new Error('Failed to create project');
  }
};

export const getProject = async (projectId: string): Promise<Project | null> => {
  try {
    const projectRef = doc(db, COLLECTIONS.PROJECTS, projectId);
    const projectSnap = await getDoc(projectRef);
    
    if (projectSnap.exists()) {
      const projectData = projectSnap.data();
      return convertTimestamps({ id: projectSnap.id, ...projectData }) as Project;
    }
    return null;
  } catch (error) {
    console.error('Error getting project:', error);
    throw new Error('Failed to get project');
  }
};

export const getUserProjects = async (userId: string): Promise<Project[]> => {
  try {
    const projectsRef = collection(db, COLLECTIONS.PROJECTS);
    const q = query(
      projectsRef,
      where('ownerId', '==', userId),
      orderBy('updatedAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as Project
    );
  } catch (error) {
    console.error('Error getting user projects:', error);
    throw new Error('Failed to get user projects');
  }
};

export const updateProject = async (projectId: string, updates: Partial<Omit<Project, 'id' | 'createdAt'>>): Promise<void> => {
  try {
    const projectRef = doc(db, COLLECTIONS.PROJECTS, projectId);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.now(),
    };

    // Handle nested timestamp conversion for knowledgeBaseStats
    if (updates.knowledgeBaseStats?.lastTrainingDate) {
      updateData.knowledgeBaseStats = {
        ...updates.knowledgeBaseStats,
        lastTrainingDate: Timestamp.fromDate(updates.knowledgeBaseStats.lastTrainingDate),
      };
    }

    // Sanitize data to remove undefined values before sending to Firestore
    const sanitizedData = sanitizeForFirestore(updateData);
    await updateDoc(projectRef, sanitizedData);
  } catch (error) {
    console.error('Error updating project:', error);
    throw new Error('Failed to update project');
  }
};

export const deleteProject = async (projectId: string): Promise<void> => {
  try {
    // Delete all chunks first
    await deleteProjectChunks(projectId);
    
    // Then delete the project
    const projectRef = doc(db, COLLECTIONS.PROJECTS, projectId);
    await deleteDoc(projectRef);
  } catch (error) {
    console.error('Error deleting project:', error);
    throw new Error('Failed to delete project');
  }
};

// Knowledge chunk operations
export const createKnowledgeChunk = async (chunkData: Omit<KnowledgeChunk, 'id'>): Promise<string> => {
  try {
    const chunksRef = collection(db, COLLECTIONS.PROJECTS, chunkData.projectId, COLLECTIONS.CHUNKS);
    const chunkWithTimestamp = {
      ...chunkData,
      createdAt: Timestamp.fromDate(chunkData.createdAt),
    };
    const docRef = await addDoc(chunksRef, chunkWithTimestamp);
    return docRef.id;
  } catch (error) {
    console.error('Error creating knowledge chunk:', error);
    throw new Error('Failed to create knowledge chunk');
  }
};

/**
 * Calculate optimal batch size based on chunk content and embedding dimensions
 * Uses conservative estimates based on actual Firestore document overhead
 */
const calculateOptimalBatchSize = (chunks: Omit<KnowledgeChunk, 'id'>[]): number => {
  if (chunks.length === 0) return 0;

  // Sample first chunk to estimate size
  const sampleChunk = chunks[0];
  const embeddingDimensions = sampleChunk.embedding.length;

  // More accurate Firestore document size estimation
  // Firestore has significant overhead for arrays and document structure
  const embeddingArrayOverhead = embeddingDimensions * 12; // ~12 bytes per number in Firestore (includes array overhead)
  const textSize = new Blob([sampleChunk.text]).size;
  const firestoreMetadataOverhead = 1000; // Conservative estimate for document structure, field names, timestamps, etc.

  // Total estimated size per document in Firestore
  const estimatedFirestoreDocSize = embeddingArrayOverhead + textSize + firestoreMetadataOverhead;

  console.log(`📊 Firestore document size calculation:`, {
    embeddingDimensions,
    embeddingArrayOverhead: `${(embeddingArrayOverhead / 1024).toFixed(1)}KB`,
    textSize: `${textSize}B`,
    firestoreMetadataOverhead: `${(firestoreMetadataOverhead / 1024).toFixed(1)}KB`,
    estimatedFirestoreDocSize: `${(estimatedFirestoreDocSize / 1024).toFixed(1)}KB`,
    totalChunks: chunks.length
  });

  // Very conservative Firestore limits to avoid transaction size errors
  // Firestore's actual limit is 10MB, but we use much smaller limits for safety
  const MAX_TRANSACTION_SIZE = 2 * 1024 * 1024; // 2MB (very conservative)
  const MAX_OPERATIONS_PER_BATCH = 50; // Much smaller batch size

  // For high-dimensional embeddings, use even more conservative limits
  let maxBatchSize;
  if (embeddingDimensions >= 3000) {
    // For 3072-dimensional embeddings, use very small batches
    maxBatchSize = Math.min(5, chunks.length);
    console.log(`🔒 High-dimensional embeddings detected (${embeddingDimensions}D), using ultra-conservative batch size: ${maxBatchSize}`);
  } else if (embeddingDimensions >= 1500) {
    // For medium-dimensional embeddings
    maxBatchSize = Math.min(10, chunks.length);
    console.log(`🔒 Medium-dimensional embeddings detected (${embeddingDimensions}D), using conservative batch size: ${maxBatchSize}`);
  } else {
    // For smaller embeddings, calculate based on size
    const maxChunksBySize = Math.floor(MAX_TRANSACTION_SIZE / estimatedFirestoreDocSize);
    maxBatchSize = Math.min(maxChunksBySize, MAX_OPERATIONS_PER_BATCH, chunks.length);
  }

  // Ensure minimum batch size of 1
  const finalBatchSize = Math.max(1, maxBatchSize);

  const estimatedBatchSize = (finalBatchSize * estimatedFirestoreDocSize) / 1024 / 1024;
  console.log(`🎯 Final batch size: ${finalBatchSize} chunks (~${estimatedBatchSize.toFixed(2)}MB estimated per batch)`);

  return finalBatchSize;
};

export const createKnowledgeChunksBatch = async (chunks: Omit<KnowledgeChunk, 'id'>[]): Promise<void> => {
  try {
    if (chunks.length === 0) {
      console.log('📝 No chunks to store');
      return;
    }

    const projectId = chunks[0].projectId;
    const optimalBatchSize = calculateOptimalBatchSize(chunks);

    console.log(`🚀 Starting batch storage: ${chunks.length} chunks in batches of ${optimalBatchSize}`);

    // Split chunks into smaller batches
    const batches: Array<Omit<KnowledgeChunk, 'id'>[]> = [];
    for (let i = 0; i < chunks.length; i += optimalBatchSize) {
      batches.push(chunks.slice(i, i + optimalBatchSize));
    }

    console.log(`📦 Created ${batches.length} batches for processing`);

    // Process batches sequentially to avoid overwhelming Firestore
    let processedChunks = 0;
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const currentBatch = batches[batchIndex];

      console.log(`⏳ Processing batch ${batchIndex + 1}/${batches.length} (${currentBatch.length} chunks)...`);

      // Attempt to process batch with fallback mechanism
      let batchProcessed = false;
      let currentBatchSize = currentBatch.length;
      let retryAttempts = 0;
      const maxRetries = 3;

      while (!batchProcessed && retryAttempts < maxRetries) {
        try {
          // If this is a retry, split the batch further
          let batchesToProcess: Array<Omit<KnowledgeChunk, 'id'>[]>;
          if (retryAttempts > 0) {
            // Progressively reduce batch size on retries
            const reducedBatchSize = Math.max(1, Math.floor(currentBatchSize / (retryAttempts + 1)));
            console.log(`🔄 Retry ${retryAttempts}: Reducing batch size from ${currentBatchSize} to ${reducedBatchSize} chunks`);

            batchesToProcess = [];
            for (let i = 0; i < currentBatch.length; i += reducedBatchSize) {
              batchesToProcess.push(currentBatch.slice(i, i + reducedBatchSize));
            }
          } else {
            batchesToProcess = [currentBatch];
          }

          // Process each sub-batch
          for (let subBatchIndex = 0; subBatchIndex < batchesToProcess.length; subBatchIndex++) {
            const subBatch = batchesToProcess[subBatchIndex];

            console.log(`⏳ Processing ${retryAttempts > 0 ? 'sub-' : ''}batch ${subBatchIndex + 1}/${batchesToProcess.length} (${subBatch.length} chunks)...`);

            const batch = writeBatch(db);

            subBatch.forEach(chunkData => {
              const chunksRef = collection(db, COLLECTIONS.PROJECTS, projectId, COLLECTIONS.CHUNKS);
              const chunkRef = doc(chunksRef);
              const chunkWithTimestamp = {
                ...chunkData,
                createdAt: Timestamp.fromDate(chunkData.createdAt),
              };
              batch.set(chunkRef, chunkWithTimestamp);
            });

            await batch.commit();
            processedChunks += subBatch.length;

            console.log(`✅ ${retryAttempts > 0 ? 'Sub-b' : 'B'}atch completed: ${processedChunks}/${chunks.length} chunks stored`);

            // Small delay between sub-batches
            if (subBatchIndex < batchesToProcess.length - 1) {
              await new Promise(resolve => setTimeout(resolve, 50));
            }
          }

          batchProcessed = true;
          console.log(`✅ Batch ${batchIndex + 1} fully completed after ${retryAttempts + 1} attempt(s)`);

        } catch (batchError) {
          retryAttempts++;
          console.error(`❌ Error in batch ${batchIndex + 1}, attempt ${retryAttempts}:`, batchError);

          if (retryAttempts >= maxRetries) {
            console.error(`❌ Failed to process batch ${batchIndex + 1} after ${maxRetries} attempts`);
            throw new Error(`Failed to store batch ${batchIndex + 1} after ${maxRetries} attempts: ${batchError instanceof Error ? batchError.message : String(batchError)}`);
          } else {
            console.log(`🔄 Will retry batch ${batchIndex + 1} with smaller sub-batches...`);
            await new Promise(resolve => setTimeout(resolve, 200)); // Wait before retry
          }
        }
      }

      // Add delay between main batches to avoid rate limiting
      if (batchIndex < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    console.log(`🎉 Successfully stored all ${processedChunks} chunks to Firestore`);

  } catch (error) {
    console.error('❌ Error creating knowledge chunks batch:', error);
    throw new Error(`Failed to create knowledge chunks: ${error instanceof Error ? error.message : String(error)}`);
  }
};

export const getProjectChunks = async (projectId: string): Promise<KnowledgeChunk[]> => {
  try {
    const chunksRef = collection(db, COLLECTIONS.PROJECTS, projectId, COLLECTIONS.CHUNKS);
    const querySnapshot = await getDocs(chunksRef);
    
    return querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as KnowledgeChunk
    );
  } catch (error) {
    console.error('Error getting project chunks:', error);
    throw new Error('Failed to get project chunks');
  }
};

export const deleteProjectChunks = async (projectId: string): Promise<void> => {
  try {
    const chunksRef = collection(db, COLLECTIONS.PROJECTS, projectId, COLLECTIONS.CHUNKS);
    const querySnapshot = await getDocs(chunksRef);
    
    const batch = writeBatch(db);
    querySnapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
  } catch (error) {
    console.error('Error deleting project chunks:', error);
    throw new Error('Failed to delete project chunks');
  }
};

export const getChunkCount = async (projectId: string): Promise<number> => {
  try {
    const chunksRef = collection(db, COLLECTIONS.PROJECTS, projectId, COLLECTIONS.CHUNKS);
    const querySnapshot = await getDocs(chunksRef);
    return querySnapshot.size;
  } catch (error) {
    console.error('Error getting chunk count:', error);
    return 0;
  }
};
