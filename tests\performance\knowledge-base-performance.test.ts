/**
 * Performance tests for knowledge base operations
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { performance } from 'perf_hooks';
import { ContentOptimizer } from '../../utils/contentOptimization';
import { globalCache } from '../../utils/cacheManager';
import { AnalyticsService } from '../../services/analyticsService';
import { fileProcessor } from '../../services/fileProcessorService';

interface PerformanceMetrics {
  duration: number;
  memoryUsage: number;
  throughput: number;
  cacheHitRate?: number;
}

describe('Knowledge Base Performance Tests', () => {
  beforeEach(() => {
    globalCache.clear();
    ContentOptimizer.resetMemoryTracking();
    AnalyticsService.clearAllData();
  });

  afterEach(() => {
    globalCache.clear();
    ContentOptimizer.resetMemoryTracking();
  });

  const measurePerformance = async <T>(
    operation: () => Promise<T>,
    expectedThroughput?: number
  ): Promise<{ result: T; metrics: PerformanceMetrics }> => {
    const startTime = performance.now();
    const startMemory = ContentOptimizer.getMemoryStats().current;
    
    const result = await operation();
    
    const endTime = performance.now();
    const endMemory = ContentOptimizer.getMemoryStats().current;
    const duration = endTime - startTime;
    
    const metrics: PerformanceMetrics = {
      duration,
      memoryUsage: endMemory - startMemory,
      throughput: expectedThroughput ? expectedThroughput / (duration / 1000) : 0,
    };

    return { result, metrics };
  };

  describe('Content Optimization Performance', () => {
    it('should process large content efficiently', async () => {
      // Create 1MB of content
      const largeContent = 'This is a test sentence for performance testing. '.repeat(20000);
      const sources = Array.from({ length: 10 }, (_, i) => ({
        sourceId: `perf-source-${i}`,
        sourceType: 'text',
        content: largeContent,
      }));

      const { result, metrics } = await measurePerformance(async () => {
        return ContentOptimizer.optimizeContent(sources, {
          enableDeduplication: true,
          maxChunkSize: 4000,
          chunkOverlap: 200,
        });
      });

      // Performance assertions
      expect(metrics.duration).toBeLessThan(10000); // Should complete within 10 seconds
      expect(metrics.memoryUsage).toBeLessThan(50 * 1024 * 1024); // Should use less than 50MB
      expect(result.chunks.length).toBeGreaterThan(0);
      expect(result.result.processingTime).toBeLessThan(metrics.duration);

      console.log(`Content Optimization Performance:
        Duration: ${metrics.duration.toFixed(2)}ms
        Memory Usage: ${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB
        Chunks Generated: ${result.chunks.length}
        Duplicates Removed: ${result.result.duplicatesRemoved}`);
    });

    it('should handle concurrent optimization requests', async () => {
      const concurrentRequests = 5;
      const contentPerRequest = 'Concurrent test content. '.repeat(1000);
      
      const requests = Array.from({ length: concurrentRequests }, (_, i) => 
        measurePerformance(async () => {
          const sources = [{
            sourceId: `concurrent-${i}`,
            sourceType: 'text',
            content: contentPerRequest,
          }];
          
          return ContentOptimizer.optimizeContent(sources, {
            enableDeduplication: false,
            maxChunkSize: 2000,
          });
        })
      );

      const startTime = performance.now();
      const results = await Promise.all(requests);
      const totalTime = performance.now() - startTime;

      // All requests should complete
      expect(results.length).toBe(concurrentRequests);
      results.forEach(({ result, metrics }) => {
        expect(result.chunks.length).toBeGreaterThan(0);
        expect(metrics.duration).toBeLessThan(5000); // Each should complete within 5 seconds
      });

      // Concurrent processing should be faster than sequential
      const sequentialTime = results.reduce((sum, { metrics }) => sum + metrics.duration, 0);
      expect(totalTime).toBeLessThan(sequentialTime * 0.8); // At least 20% improvement

      console.log(`Concurrent Optimization Performance:
        Concurrent Time: ${totalTime.toFixed(2)}ms
        Sequential Time: ${sequentialTime.toFixed(2)}ms
        Improvement: ${((sequentialTime - totalTime) / sequentialTime * 100).toFixed(1)}%`);
    });

    it('should maintain performance with deduplication enabled', async () => {
      // Create content with many duplicates
      const baseContent = 'This is duplicate content that appears multiple times. ';
      const uniqueContent = 'This is unique content for chunk ';
      
      const sources = Array.from({ length: 20 }, (_, i) => ({
        sourceId: `dup-source-${i}`,
        sourceType: 'text',
        content: baseContent.repeat(10) + uniqueContent + i,
      }));

      const { result: withDedup, metrics: dedupMetrics } = await measurePerformance(async () => {
        return ContentOptimizer.optimizeContent(sources, {
          enableDeduplication: true,
          maxChunkSize: 1000,
        });
      });

      const { result: withoutDedup, metrics: noDedupMetrics } = await measurePerformance(async () => {
        return ContentOptimizer.optimizeContent(sources, {
          enableDeduplication: false,
          maxChunkSize: 1000,
        });
      });

      // Deduplication should remove duplicates
      expect(withDedup.result.duplicatesRemoved).toBeGreaterThan(0);
      expect(withDedup.chunks.length).toBeLessThan(withoutDedup.chunks.length);
      
      // Performance impact should be reasonable (less than 2x slower)
      expect(dedupMetrics.duration).toBeLessThan(noDedupMetrics.duration * 2);

      console.log(`Deduplication Performance Impact:
        With Dedup: ${dedupMetrics.duration.toFixed(2)}ms (${withDedup.chunks.length} chunks)
        Without Dedup: ${noDedupMetrics.duration.toFixed(2)}ms (${withoutDedup.chunks.length} chunks)
        Duplicates Removed: ${withDedup.result.duplicatesRemoved}
        Performance Overhead: ${((dedupMetrics.duration / noDedupMetrics.duration - 1) * 100).toFixed(1)}%`);
    });
  });

  describe('Cache Performance', () => {
    it('should handle high-frequency cache operations', async () => {
      const operationCount = 10000;
      const keyPrefix = 'perf-test';
      
      const { metrics } = await measurePerformance(async () => {
        // Perform many cache operations
        for (let i = 0; i < operationCount; i++) {
          const key = `${keyPrefix}-${i % 1000}`; // Reuse keys to test hit rate
          const value = `value-${i}`;
          
          globalCache.set(key, value);
          globalCache.get(key);
        }
        
        return operationCount;
      }, operationCount);

      const cacheMetrics = globalCache.getMetrics();
      
      // Performance assertions
      expect(metrics.duration).toBeLessThan(5000); // Should complete within 5 seconds
      expect(metrics.throughput).toBeGreaterThan(1000); // At least 1000 ops/second
      expect(cacheMetrics.hitRate).toBeGreaterThan(50); // Should have decent hit rate

      console.log(`Cache Performance:
        Operations: ${operationCount}
        Duration: ${metrics.duration.toFixed(2)}ms
        Throughput: ${metrics.throughput.toFixed(0)} ops/sec
        Hit Rate: ${cacheMetrics.hitRate.toFixed(1)}%
        Memory Usage: ${(cacheMetrics.memoryUsage.used / 1024 / 1024).toFixed(2)}MB`);
    });

    it('should efficiently handle cache eviction under memory pressure', async () => {
      const largeValueSize = 1024 * 1024; // 1MB per value
      const maxValues = 60; // Try to store 60MB (should trigger eviction)
      
      const { metrics } = await measurePerformance(async () => {
        for (let i = 0; i < maxValues; i++) {
          const key = `large-value-${i}`;
          const value = 'x'.repeat(largeValueSize);
          
          const success = globalCache.set(key, value, {
            priority: i % 3 === 0 ? 'high' : 'normal',
          });
          
          // Some sets might fail due to size limits
          if (!success && i < 10) {
            throw new Error('Cache should accept at least first 10 values');
          }
        }
        
        return maxValues;
      });

      const cacheMetrics = globalCache.getMetrics();
      
      // Should handle memory pressure gracefully
      expect(metrics.duration).toBeLessThan(10000); // Should complete within 10 seconds
      expect(cacheMetrics.evictionCount).toBeGreaterThan(0); // Should have evicted some entries
      expect(cacheMetrics.memoryUsage.percentage).toBeLessThan(100); // Should not exceed memory limit

      console.log(`Cache Eviction Performance:
        Attempted Entries: ${maxValues}
        Actual Entries: ${cacheMetrics.totalEntries}
        Evictions: ${cacheMetrics.evictionCount}
        Memory Usage: ${cacheMetrics.memoryUsage.percentage.toFixed(1)}%
        Duration: ${metrics.duration.toFixed(2)}ms`);
    });
  });

  describe('File Processing Performance', () => {
    it('should process multiple files efficiently', async () => {
      const fileCount = 50;
      const fileSize = 10 * 1024; // 10KB per file
      
      const files = Array.from({ length: fileCount }, (_, i) => {
        const content = `File ${i} content. `.repeat(fileSize / 20);
        return new File([content], `test-${i}.txt`, { type: 'text/plain' });
      });

      const { metrics } = await measurePerformance(async () => {
        const results = await Promise.all(
          files.map(file => fileProcessor.processFile(file))
        );
        
        return results;
      }, fileCount);

      // Performance assertions
      expect(metrics.duration).toBeLessThan(15000); // Should complete within 15 seconds
      expect(metrics.throughput).toBeGreaterThan(2); // At least 2 files/second

      console.log(`File Processing Performance:
        Files: ${fileCount}
        Total Size: ${(fileCount * fileSize / 1024).toFixed(1)}KB
        Duration: ${metrics.duration.toFixed(2)}ms
        Throughput: ${metrics.throughput.toFixed(1)} files/sec
        Avg per File: ${(metrics.duration / fileCount).toFixed(2)}ms`);
    });

    it('should handle large files without memory issues', async () => {
      const largeFileSize = 5 * 1024 * 1024; // 5MB
      const content = 'Large file content line. '.repeat(largeFileSize / 25);
      const largeFile = new File([content], 'large-test.txt', { type: 'text/plain' });

      const { result, metrics } = await measurePerformance(async () => {
        return fileProcessor.processFile(largeFile);
      });

      // Should process successfully
      expect(result.content.length).toBeGreaterThan(0);
      expect(result.metadata.fileSize).toBe(largeFileSize);
      
      // Performance should be reasonable
      expect(metrics.duration).toBeLessThan(10000); // Should complete within 10 seconds
      expect(metrics.memoryUsage).toBeLessThan(largeFileSize * 3); // Should not use more than 3x file size

      console.log(`Large File Processing Performance:
        File Size: ${(largeFileSize / 1024 / 1024).toFixed(1)}MB
        Duration: ${metrics.duration.toFixed(2)}ms
        Memory Usage: ${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB
        Processing Rate: ${(largeFileSize / 1024 / 1024 / (metrics.duration / 1000)).toFixed(1)}MB/sec`);
    });
  });

  describe('Analytics Performance', () => {
    it('should handle high-volume event tracking', async () => {
      const eventCount = 5000;
      const projectIds = ['proj-1', 'proj-2', 'proj-3'];
      
      const { metrics } = await measurePerformance(async () => {
        for (let i = 0; i < eventCount; i++) {
          AnalyticsService.trackEvent({
            projectId: projectIds[i % projectIds.length],
            sessionId: `session-${Math.floor(i / 100)}`,
            eventType: ['search', 'build', 'source_add'][i % 3] as any,
            eventData: {
              success: Math.random() > 0.1,
              duration: Math.random() * 1000,
              query: i % 4 === 0 ? `query-${i}` : undefined,
            },
          });
        }
        
        return eventCount;
      }, eventCount);

      // Performance assertions
      expect(metrics.duration).toBeLessThan(5000); // Should complete within 5 seconds
      expect(metrics.throughput).toBeGreaterThan(500); // At least 500 events/second

      // Test report generation performance
      const reportStartTime = performance.now();
      const reports = projectIds.map(id => AnalyticsService.generateQualityReport(id));
      const reportTime = performance.now() - reportStartTime;

      expect(reportTime).toBeLessThan(1000); // Reports should generate quickly
      expect(reports.length).toBe(projectIds.length);

      console.log(`Analytics Performance:
        Events Tracked: ${eventCount}
        Tracking Duration: ${metrics.duration.toFixed(2)}ms
        Tracking Throughput: ${metrics.throughput.toFixed(0)} events/sec
        Report Generation: ${reportTime.toFixed(2)}ms for ${projectIds.length} projects`);
    });

    it('should efficiently query usage statistics', async () => {
      const projectId = 'perf-test-project';
      
      // Generate test data
      for (let i = 0; i < 1000; i++) {
        AnalyticsService.trackEvent({
          projectId,
          sessionId: `session-${Math.floor(i / 50)}`,
          eventType: ['search', 'build'][i % 2] as any,
          eventData: {
            success: true,
            duration: Math.random() * 2000,
            query: i % 2 === 0 ? `test query ${i % 20}` : undefined,
          },
        });
      }

      const { result, metrics } = await measurePerformance(async () => {
        const [dayStats, weekStats, monthStats] = await Promise.all([
          Promise.resolve(AnalyticsService.getUsageStatistics(projectId, 'day')),
          Promise.resolve(AnalyticsService.getUsageStatistics(projectId, 'week')),
          Promise.resolve(AnalyticsService.getUsageStatistics(projectId, 'month')),
        ]);
        
        return { dayStats, weekStats, monthStats };
      });

      // Should generate statistics quickly
      expect(metrics.duration).toBeLessThan(1000); // Should complete within 1 second
      expect(result.dayStats.searches.total).toBeGreaterThan(0);
      expect(result.weekStats.builds.total).toBeGreaterThan(0);

      console.log(`Usage Statistics Performance:
        Events Processed: 1000
        Query Duration: ${metrics.duration.toFixed(2)}ms
        Day Stats: ${result.dayStats.searches.total} searches, ${result.dayStats.builds.total} builds
        Week Stats: ${result.weekStats.searches.total} searches, ${result.weekStats.builds.total} builds`);
    });
  });

  describe('Memory Management Performance', () => {
    it('should maintain stable memory usage under sustained load', async () => {
      const iterations = 100;
      const memorySnapshots: number[] = [];
      
      for (let i = 0; i < iterations; i++) {
        // Perform memory-intensive operations
        const content = 'Memory test content. '.repeat(1000);
        const sources = [{
          sourceId: `memory-test-${i}`,
          sourceType: 'text',
          content,
        }];
        
        ContentOptimizer.optimizeContent(sources, {
          enableDeduplication: true,
          maxChunkSize: 2000,
        });
        
        // Take memory snapshot every 10 iterations
        if (i % 10 === 0) {
          const memoryStats = ContentOptimizer.getMemoryStats();
          memorySnapshots.push(memoryStats.current);
        }
        
        // Force cleanup periodically
        if (i % 25 === 0) {
          globalCache.optimize();
          ContentOptimizer.resetMemoryTracking();
        }
      }

      // Memory should not grow unbounded
      const maxMemory = Math.max(...memorySnapshots);
      const avgMemory = memorySnapshots.reduce((a, b) => a + b, 0) / memorySnapshots.length;
      const memoryGrowth = memorySnapshots[memorySnapshots.length - 1] - memorySnapshots[0];

      expect(maxMemory).toBeLessThan(100 * 1024 * 1024); // Should not exceed 100MB
      expect(Math.abs(memoryGrowth)).toBeLessThan(50 * 1024 * 1024); // Growth should be limited

      console.log(`Memory Management Performance:
        Iterations: ${iterations}
        Max Memory: ${(maxMemory / 1024 / 1024).toFixed(2)}MB
        Avg Memory: ${(avgMemory / 1024 / 1024).toFixed(2)}MB
        Memory Growth: ${(memoryGrowth / 1024 / 1024).toFixed(2)}MB
        Snapshots: ${memorySnapshots.length}`);
    });
  });
});
