/**
 * Source tracking service for incremental knowledge base updates
 */

import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  query,
  where,
  deleteDoc,
  updateDoc,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import { DataSource, SourceType } from '../types/knowledgeBase';

export interface TrackedSource {
  id: string;
  projectId: string;
  sourceId: string;
  sourceType: SourceType;
  reference: string;
  contentHash: string;
  lastProcessed: Date;
  chunkIds: string[];
  version: number;
  metadata?: {
    fileSize?: number;
    lastModified?: Date;
    encoding?: string;
    processingTime?: number;
    chunkCount?: number;
  };
}

export interface SourceChangeDetection {
  hasChanged: boolean;
  changeType: 'new' | 'modified' | 'unchanged' | 'deleted';
  previousVersion?: TrackedSource;
  currentHash?: string;
  details?: string;
}

export interface IncrementalUpdateOptions {
  forceUpdate?: boolean;
  skipUnchanged?: boolean;
  batchSize?: number;
  preserveExistingChunks?: boolean;
}

export interface IncrementalUpdateResult {
  totalSources: number;
  newSources: number;
  modifiedSources: number;
  unchangedSources: number;
  deletedSources: number;
  chunksAdded: number;
  chunksUpdated: number;
  chunksRemoved: number;
  processingTime: number;
}

export class SourceTrackingService {
  private static readonly COLLECTION_NAME = 'sourceTracking';

  /**
   * Check if source tracking is available for the current user
   */
  public static isSourceTrackingAvailable(): boolean {
    try {
      const currentUser = auth.currentUser;
      return currentUser !== null;
    } catch (error) {
      console.warn('Error checking source tracking availability:', error);
      return false;
    }
  }

  /**
   * Generate a hash for content to detect changes
   */
  private static generateContentHash(content: string): string {
    let hash = 0;
    if (content.length === 0) return hash.toString();
    
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * Track a processed source
   */
  public static async trackSource(
    projectId: string,
    source: DataSource,
    chunkIds: string[],
    processingTime?: number
  ): Promise<void> {
    try {
      // Get current user UID for security rules
      const currentUser = auth.currentUser;
      if (!currentUser) {
        console.warn('No authenticated user for source tracking');
        return;
      }

      const contentHash = this.generateContentHash(source.content || '');

      const trackedSource: TrackedSource = {
        id: `${projectId}_${source.id}`,
        projectId,
        sourceId: source.id,
        sourceType: source.type,
        reference: source.reference,
        contentHash,
        lastProcessed: new Date(),
        chunkIds,
        version: 1,
        metadata: {
          processingTime,
          chunkCount: chunkIds.length,
          ...(source.metadata || {})
        }
      };

      // Check if source already exists to increment version
      const existingSource = await this.getTrackedSource(projectId, source.id);
      if (existingSource) {
        trackedSource.version = existingSource.version + 1;
      }

      const docRef = doc(db, this.COLLECTION_NAME, trackedSource.id);
      await setDoc(docRef, {
        ...trackedSource,
        lastProcessed: serverTimestamp(),
        ownerId: currentUser.uid, // Use current user's UID for security rules
      });
    } catch (error) {
      console.error('Error tracking source:', error);
      // Don't throw error to prevent breaking the main workflow
      // Source tracking is optional functionality
    }
  }

  /**
   * Get a tracked source
   */
  public static async getTrackedSource(
    projectId: string,
    sourceId: string
  ): Promise<TrackedSource | null> {
    try {
      // Check authentication first
      const currentUser = auth.currentUser;
      if (!currentUser) {
        console.warn('No authenticated user for getting tracked source');
        return null;
      }

      const docRef = doc(db, this.COLLECTION_NAME, `${projectId}_${sourceId}`);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const data = docSnap.data();

        // Verify the document belongs to the current user
        if (data.ownerId !== currentUser.uid) {
          console.warn('Access denied: tracked source does not belong to current user');
          return null;
        }

        return {
          ...data,
          lastProcessed: data.lastProcessed?.toDate() || new Date(),
        } as TrackedSource;
      }

      return null;
    } catch (error) {
      console.error('Error getting tracked source:', error);
      return null;
    }
  }

  /**
   * Get all tracked sources for a project
   */
  public static async getProjectSources(projectId: string): Promise<TrackedSource[]> {
    try {
      // Check authentication
      const currentUser = auth.currentUser;
      if (!currentUser) {
        console.warn('No authenticated user for getting project sources');
        return [];
      }

      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('projectId', '==', projectId),
        where('ownerId', '==', currentUser.uid)
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          lastProcessed: data.lastProcessed?.toDate() || new Date(),
        } as TrackedSource;
      });
    } catch (error) {
      console.error('Error getting project sources:', error);
      return [];
    }
  }

  /**
   * Detect changes in a source
   */
  public static async detectSourceChanges(
    projectId: string,
    source: DataSource
  ): Promise<SourceChangeDetection> {
    try {
      // Check authentication first
      const currentUser = auth.currentUser;
      if (!currentUser) {
        console.warn('No authenticated user for detecting source changes');
        return {
          hasChanged: true,
          changeType: 'new',
          currentHash: this.generateContentHash(source.content || ''),
          details: 'Source treated as new due to authentication unavailability'
        };
      }

      const trackedSource = await this.getTrackedSource(projectId, source.id);
      const currentHash = this.generateContentHash(source.content || '');

      if (!trackedSource) {
        return {
          hasChanged: true,
          changeType: 'new',
          currentHash,
          details: 'Source is new and has not been processed before'
        };
      }

      if (trackedSource.contentHash !== currentHash) {
        return {
          hasChanged: true,
          changeType: 'modified',
          previousVersion: trackedSource,
          currentHash,
          details: 'Source content has been modified since last processing'
        };
      }

      return {
        hasChanged: false,
        changeType: 'unchanged',
        previousVersion: trackedSource,
        currentHash,
        details: 'Source content has not changed since last processing'
      };
    } catch (error) {
      console.error('Error detecting source changes:', error);
      // Return safe fallback
      return {
        hasChanged: true,
        changeType: 'new',
        currentHash: this.generateContentHash(source.content || ''),
        details: 'Source treated as new due to detection error'
      };
    }
  }

  /**
   * Remove tracking for a source
   */
  public static async untrackSource(projectId: string, sourceId: string): Promise<void> {
    const docRef = doc(db, this.COLLECTION_NAME, `${projectId}_${sourceId}`);
    await deleteDoc(docRef);
  }

  /**
   * Update chunk IDs for a tracked source
   */
  public static async updateSourceChunks(
    projectId: string,
    sourceId: string,
    chunkIds: string[]
  ): Promise<void> {
    const docRef = doc(db, this.COLLECTION_NAME, `${projectId}_${sourceId}`);
    await updateDoc(docRef, {
      chunkIds,
      lastProcessed: serverTimestamp(),
    });
  }

  /**
   * Perform incremental update analysis
   */
  public static async analyzeIncrementalUpdate(
    projectId: string,
    sources: DataSource[]
  ): Promise<{
    newSources: DataSource[];
    modifiedSources: DataSource[];
    unchangedSources: DataSource[];
    orphanedSources: TrackedSource[];
    totalSources: number;
    estimatedProcessingTime: number;
    recommendations: string[];
  }> {
    try {
      // Check authentication first
      const currentUser = auth.currentUser;
      if (!currentUser) {
        console.warn('No authenticated user for incremental analysis');
        // Return fallback analysis
        return {
          newSources: sources,
          modifiedSources: [],
          unchangedSources: [],
          orphanedSources: [],
          totalSources: sources.length,
          estimatedProcessingTime: sources.length * 30,
          recommendations: ['All sources will be processed as new due to authentication unavailability'],
        };
      }

      const trackedSources = await this.getProjectSources(projectId);
      const trackedSourceIds = new Set(trackedSources.map(ts => ts.sourceId));
      const currentSourceIds = new Set(sources.map(s => s.id));

      const newSources: DataSource[] = [];
      const modifiedSources: DataSource[] = [];
      const unchangedSources: DataSource[] = [];

      // Analyze current sources
      for (const source of sources) {
        const changeDetection = await this.detectSourceChanges(projectId, source);

        switch (changeDetection.changeType) {
          case 'new':
            newSources.push(source);
            break;
          case 'modified':
            modifiedSources.push(source);
            break;
          case 'unchanged':
            unchangedSources.push(source);
            break;
        }
      }

      // Find orphaned sources (tracked but no longer in current sources)
      const orphanedSources = trackedSources.filter(
        ts => !currentSourceIds.has(ts.sourceId)
      );

      // Generate recommendations
      const recommendations: string[] = [];
      if (newSources.length > 0) {
        recommendations.push(`${newSources.length} new sources will be processed`);
      }
      if (modifiedSources.length > 0) {
        recommendations.push(`${modifiedSources.length} modified sources will be reprocessed`);
      }
      if (unchangedSources.length > 0) {
        recommendations.push(`${unchangedSources.length} unchanged sources will be skipped`);
      }
      if (orphanedSources.length > 0) {
        recommendations.push(`${orphanedSources.length} orphaned sources will be cleaned up`);
      }

      return {
        newSources,
        modifiedSources,
        unchangedSources,
        orphanedSources,
        totalSources: sources.length,
        estimatedProcessingTime: (newSources.length + modifiedSources.length) * 30,
        recommendations,
      };
    } catch (error) {
      console.error('Error in incremental analysis:', error);
      // Return fallback analysis on error
      return {
        newSources: sources,
        modifiedSources: [],
        unchangedSources: [],
        orphanedSources: [],
        totalSources: sources.length,
        estimatedProcessingTime: sources.length * 30,
        recommendations: ['All sources will be processed as new due to analysis error'],
      };
    }
  }

  /**
   * Clean up orphaned source tracking
   */
  public static async cleanupOrphanedSources(
    projectId: string,
    orphanedSources: TrackedSource[]
  ): Promise<void> {
    const deletePromises = orphanedSources.map(source =>
      this.untrackSource(projectId, source.sourceId)
    );
    
    await Promise.all(deletePromises);
  }

  /**
   * Get source processing statistics
   */
  public static async getSourceStatistics(projectId: string): Promise<{
    totalSources: number;
    totalChunks: number;
    averageChunksPerSource: number;
    lastProcessingDate?: Date;
    sourcesByType: Record<SourceType, number>;
    processingTimes: {
      average: number;
      min: number;
      max: number;
    };
  }> {
    const sources = await this.getProjectSources(projectId);
    
    if (sources.length === 0) {
      return {
        totalSources: 0,
        totalChunks: 0,
        averageChunksPerSource: 0,
        sourcesByType: {
          [SourceType.URL]: 0,
          [SourceType.TEXT]: 0,
          [SourceType.FILE]: 0,
        },
        processingTimes: { average: 0, min: 0, max: 0 }
      };
    }
    
    const totalChunks = sources.reduce((sum, source) => sum + source.chunkIds.length, 0);
    const averageChunksPerSource = totalChunks / sources.length;
    const lastProcessingDate = new Date(Math.max(...sources.map(s => s.lastProcessed.getTime())));
    
    const sourcesByType = sources.reduce((acc, source) => {
      acc[source.sourceType] = (acc[source.sourceType] || 0) + 1;
      return acc;
    }, {} as Record<SourceType, number>);
    
    // Ensure all source types are represented
    Object.values(SourceType).forEach(type => {
      if (!(type in sourcesByType)) {
        sourcesByType[type] = 0;
      }
    });
    
    const processingTimes = sources
      .map(s => s.metadata?.processingTime || 0)
      .filter(time => time > 0);
    
    const processingStats = processingTimes.length > 0 ? {
      average: processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length,
      min: Math.min(...processingTimes),
      max: Math.max(...processingTimes)
    } : { average: 0, min: 0, max: 0 };
    
    return {
      totalSources: sources.length,
      totalChunks,
      averageChunksPerSource,
      lastProcessingDate,
      sourcesByType,
      processingTimes: processingStats
    };
  }
}
