# Troubleshooting Guide

## Console Errors Fixed

### ✅ Fixed: Firebase Permission Errors
**Error**: `FirebaseError: Missing or insufficient permissions`

**Root Cause**: Firestore security rules didn't include permissions for source tracking collections.

**Solution Applied**:
1. Updated `firestore.rules` to include source tracking permissions
2. Added graceful error handling in `SourceTrackingService`
3. Modified `DataSourceManager` to handle permission errors gracefully

**What happens now**:
- If permissions are available, source tracking works normally
- If permissions are missing, the system falls back to processing all sources as new
- Users see a warning toast instead of errors

### ✅ Fixed: Global is not defined
**Error**: `ReferenceError: global is not defined`

**Root Cause**: `ContentOptimizer` tried to access Node.js-specific `global.gc` in browser environment.

**Solution Applied**:
```typescript
// Before (BROKEN)
if (global.gc) {
  global.gc();
}

// After (FIXED)
if (typeof global !== 'undefined' && global.gc) {
  global.gc();
}
```

### ✅ Fixed: progressTracker.updateStepProgress is not a function
**Error**: `TypeError: progressTracker.updateStepProgress is not a function`

**Root Cause**: `useProgressTracker` hook didn't export the `updateStepProgress` method.

**Solution Applied**:
1. Added `updateStepProgress` method to the hook
2. Exported it in the return object

## Deployment Instructions

### Option 1: Automatic Deployment (Recommended)
```bash
node deploy-firestore-rules.js
```

### Option 2: Manual Deployment
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your "ecomqna" project
3. Navigate to **Firestore Database** > **Rules**
4. Replace existing rules with the content from `firestore.rules`
5. Click **Publish**

### Option 3: Firebase CLI
```bash
firebase deploy --only firestore:rules
```

## Common Issues and Solutions

### Issue: Still Getting Permission Errors
**Symptoms**: Console shows Firebase permission errors after rule deployment

**Solutions**:
1. **Wait for propagation**: Rules can take 1-2 minutes to propagate
2. **Clear browser cache**: Clear localStorage and cookies for localhost
3. **Restart dev server**: Stop and restart `npm run dev`
4. **Check authentication**: Ensure user is properly logged in
5. **Verify rules**: Check Firebase Console to confirm rules were published

### Issue: Source Tracking Not Working
**Symptoms**: All sources show as "new" even when unchanged

**Expected Behavior**: This is now the fallback behavior when permissions are insufficient

**Solutions**:
1. **Deploy updated rules**: Follow deployment instructions above
2. **Check project ownership**: Ensure logged-in user owns the project
3. **Verify authentication**: User must be authenticated to access tracking

### Issue: Build Process Fails
**Symptoms**: Knowledge base build fails with various errors

**Solutions**:
1. **Check internet connection**: Required for URL fetching
2. **Verify file formats**: Ensure files are supported (txt, md, html, csv)
3. **Check file sizes**: Files must be under 10MB
4. **Reduce batch size**: Lower concurrent processing if memory issues occur

### Issue: Performance Problems
**Symptoms**: Slow processing or high memory usage

**Solutions**:
1. **Enable incremental updates**: Only process changed sources
2. **Use smaller batch sizes**: Reduce concurrent processing
3. **Clear cache**: Reset cache if it becomes too large
4. **Optimize content**: Remove duplicate or low-quality sources

## Development Environment Setup

### Prerequisites
- Node.js 16+ installed
- Firebase CLI installed: `npm install -g firebase-tools`
- Valid Firebase project with Firestore enabled

### Environment Variables
Ensure `.env` file contains:
```
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=ecomqna.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=ecomqna
VITE_FIREBASE_STORAGE_BUCKET=ecomqna.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_GEMINI_API_KEY=your_gemini_key
```

### Firebase Authentication
1. **Login**: `firebase login`
2. **Select project**: `firebase use ecomqna`
3. **Deploy rules**: `firebase deploy --only firestore:rules`

## Error Categories and Handling

### Network Errors
- **Symptoms**: Failed to fetch URLs, timeout errors
- **Handling**: Automatic retry with exponential backoff
- **User Action**: Check internet connection, verify URL accessibility

### Validation Errors
- **Symptoms**: Invalid file format, content too large
- **Handling**: Immediate feedback with specific guidance
- **User Action**: Fix input according to error message

### Permission Errors
- **Symptoms**: Firestore access denied
- **Handling**: Graceful fallback with warning message
- **User Action**: Deploy updated Firestore rules

### Processing Errors
- **Symptoms**: Content extraction fails, embedding generation fails
- **Handling**: Retry logic with detailed error reporting
- **User Action**: Check content quality, reduce batch size

## Performance Optimization

### Memory Management
- **Batch Processing**: Large content is processed in chunks
- **Cache Optimization**: LRU cache with automatic cleanup
- **Memory Monitoring**: Real-time memory usage tracking

### Processing Optimization
- **Parallel Processing**: Multiple sources processed concurrently
- **Incremental Updates**: Only changed sources are reprocessed
- **Content Deduplication**: Removes redundant content automatically

### Caching Strategy
- **Multi-level Caching**: Component, service, and content caching
- **Intelligent Eviction**: Priority-based cache management
- **Cache Metrics**: Performance monitoring and optimization

## Monitoring and Analytics

### Built-in Monitoring
- **Performance Metrics**: Build times, memory usage, cache hit rates
- **Quality Metrics**: Content scores, duplicate detection
- **Error Tracking**: Categorized error reporting with context

### Analytics Dashboard
- **Real-time Metrics**: Current system performance
- **Usage Statistics**: User behavior and feature adoption
- **Quality Reports**: Content analysis and recommendations

## Getting Help

### Self-Diagnosis
1. **Check console**: Look for specific error messages
2. **Review logs**: Check browser developer tools
3. **Test connectivity**: Verify internet and Firebase access
4. **Validate input**: Ensure content meets requirements

### Support Resources
- **Documentation**: Comprehensive guides in `/docs` folder
- **Error Messages**: Include specific guidance and next steps
- **Troubleshooting**: This guide covers common issues
- **Code Comments**: Detailed inline documentation

### Reporting Issues
When reporting issues, include:
1. **Error message**: Complete console error output
2. **Steps to reproduce**: Exact sequence of actions
3. **Environment**: Browser, OS, Node.js version
4. **Content details**: File types, sizes, source types
5. **Expected behavior**: What should have happened

## Best Practices

### Development
- **Use TypeScript**: Leverage type safety for better development
- **Test thoroughly**: Use provided test suites for validation
- **Monitor performance**: Watch memory usage and processing times
- **Handle errors gracefully**: Provide meaningful user feedback

### Production
- **Deploy rules first**: Ensure Firestore rules are updated
- **Monitor analytics**: Track performance and quality metrics
- **Regular maintenance**: Clean up old sources and cache
- **User training**: Provide clear guidance on best practices

### Content Management
- **Quality first**: Focus on high-quality, relevant content
- **Regular updates**: Keep content fresh and accurate
- **Organize logically**: Group related content together
- **Monitor metrics**: Use analytics to guide improvements
