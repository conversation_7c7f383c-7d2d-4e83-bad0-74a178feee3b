import React, { useState, useEffect } from 'react';
import { Project, ProjectStatus } from '../types';
import {
  processAndStoreContent,
  getProjectKnowledgeBaseStats,
  clearProjectKnowledgeBase
} from '../services/projectService';
import { ProcessIcon, WarningIcon } from './Icons';
import { useToast } from './Toast';
import { EnhancedErrorHandler } from '../utils/errorHandling';
import { ContentOptimizer } from '../utils/contentOptimization';
import { ProgressTracker, useProgressTracker } from './ProgressTracker';
import { DataSourceManager, DataSource, SourceType } from './DataSourceManager';
import {
  KnowledgeBaseStats,
  BuildKnowledgeBaseOptions,
  ProcessingProgress
} from '../types/knowledgeBase';

interface KnowledgeBaseTabProps {
  project: Project;
  onProjectUpdate: (updatedProject: Project) => void;
}

export const KnowledgeBaseTab: React.FC<KnowledgeBaseTabProps> = ({
  project,
  onProjectUpdate
}) => {
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<KnowledgeBaseStats>(project.knowledgeBaseStats);
  const [abortController, setAbortController] = useState<AbortController | null>(null);

  const { addToast } = useToast();

  // Enhanced progress tracking
  const progressTracker = useProgressTracker([
    { id: 'prepare', label: 'Preparing', description: 'Validating sources and preparing for processing' },
    { id: 'extract', label: 'Extracting', description: 'Extracting content from all sources' },
    { id: 'chunk', label: 'Chunking', description: 'Breaking content into chunks for processing' },
    { id: 'embed', label: 'Embedding', description: 'Generating AI embeddings for semantic search' },
    { id: 'store', label: 'Storing', description: 'Saving processed data to knowledge base' },
    { id: 'finalize', label: 'Finalizing', description: 'Updating project status and cleaning up' },
  ]);

  // Load current stats
  useEffect(() => {
    const loadStats = async () => {
      try {
        const currentStats = await getProjectKnowledgeBaseStats(project.id);
        setStats(currentStats);
      } catch (err) {
        console.error('Error loading stats:', err);
      }
    };
    
    loadStats();
  }, [project.id]);

  const handleDataSourcesChange = (sources: DataSource[]) => {
    setDataSources(sources);
    setError(null); // Clear any previous errors when sources change
  };

  const handleBuildKnowledgeBase = async () => {
    if (dataSources.length === 0) {
      const errorDetails = EnhancedErrorHandler.analyzeError(new Error('Please add at least one data source'));
      setError(errorDetails.message);
      addToast({
        type: errorDetails.type,
        title: errorDetails.title,
        message: errorDetails.message,
      });
      return;
    }

    setIsProcessing(true);
    setError(null);
    setProcessingStatus('');

    // Create abort controller for cancellation support
    const controller = new AbortController();
    setAbortController(controller);

    try {
      // Step 1: Prepare sources
      progressTracker.reset();
      progressTracker.startStep('prepare', 'Validating and preparing data sources');
      await new Promise(resolve => setTimeout(resolve, 500));
      progressTracker.completeStep('prepare');
      progressTracker.nextStep();

      // Step 2: Extract content from all sources (with parallel processing)
      progressTracker.startStep('extract', 'Extracting content from all sources');

      // Process sources in parallel for better performance
      const sourceProcessingPromises = dataSources.map(async (source, index) => {
        try {
          if (controller.signal.aborted) {
            throw new Error('Operation was cancelled');
          }

          let content = '';

          if (source.type === SourceType.URL) {
            // For URLs, we need to fetch the content
            const response = await EnhancedErrorHandler.withRetry(
              async () => {
                const res = await fetch(source.reference, { signal: controller.signal });
                if (!res.ok) {
                  throw new Error(`Failed to fetch URL: ${res.status} ${res.statusText}`);
                }
                return res.text();
              },
              { maxAttempts: 3 },
              (attempt) => {
                addToast({
                  type: 'info',
                  title: 'Retrying URL Fetch',
                  message: `Retrying ${source.reference} (attempt ${attempt + 1})`,
                });
              }
            );
            content = response;
          } else {
            // For text and file sources, content is already stored
            content = source.content || '';
          }

          return {
            success: true,
            source,
            content: content.trim(),
            index
          };
        } catch (sourceError) {
          console.error(`Error processing source ${source.reference}:`, sourceError);
          const errorDetails = EnhancedErrorHandler.analyzeError(
            sourceError instanceof Error ? sourceError : new Error(String(sourceError))
          );

          return {
            success: false,
            source,
            error: `${source.type.toUpperCase()}: ${source.reference} (${errorDetails.message})`,
            index
          };
        }
      });

      // Wait for all sources to be processed
      const sourceResults = await Promise.allSettled(sourceProcessingPromises);

      // Process results
      let allContent = '';
      const failedSources: string[] = [];
      const successfulSources: Array<{ source: DataSource; content: string }> = [];

      sourceResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const sourceResult = result.value;
          if (sourceResult.success && sourceResult.content) {
            allContent += sourceResult.content + '\n\n';
            successfulSources.push({
              source: sourceResult.source,
              content: sourceResult.content
            });
          } else if (!sourceResult.success) {
            failedSources.push(sourceResult.error);
          }
        } else {
          const source = dataSources[index];
          failedSources.push(`${source.type.toUpperCase()}: ${source.reference} (Promise rejected: ${result.reason})`);
        }
      });

      // Update progress with processing statistics
      const totalSources = dataSources.length;
      const successfulCount = successfulSources.length;
      const failedCount = failedSources.length;

      addToast({
        type: 'info',
        title: 'Content Extraction Complete',
        message: `Processed ${totalSources} sources: ${successfulCount} successful, ${failedCount} failed`,
        duration: 3000,
      });

      progressTracker.completeStep('extract');
      progressTracker.nextStep();

      // Validate extracted content
      if (!allContent.trim()) {
        const errorMessage = failedSources.length > 0
          ? `No content could be extracted from the provided sources:\n${failedSources.join('\n')}`
          : 'No content could be extracted from the provided sources';
        throw new Error(errorMessage);
      }

      // Handle partial failures
      if (failedSources.length > 0) {
        console.warn('Some sources failed to process:', failedSources);
        addToast({
          type: 'warning',
          title: 'Partial Success',
          message: `${failedSources.length} source(s) failed to process. Continuing with available content.`,
          duration: 4000,
        });
      }

      // Step 3: Optimize and chunk content
      progressTracker.startStep('chunk', 'Optimizing and chunking content for processing');

      // Prepare sources for optimization
      const sourcesForOptimization = successfulSources.map(item => ({
        sourceId: item.source.id,
        sourceType: item.source.type,
        content: item.content
      }));

      // Estimate memory usage and determine optimal batch size
      const totalContentSize = allContent.length;
      const memoryEstimate = ContentOptimizer.estimateMemoryUsage(totalContentSize, {
        maxChunkSize: 4000,
        chunkOverlap: 200,
        enableDeduplication: true
      });

      addToast({
        type: 'info',
        title: 'Content Analysis',
        message: `Estimated ${memoryEstimate.estimatedChunks} chunks, ${memoryEstimate.estimatedMemoryMB.toFixed(1)}MB memory usage`,
        duration: 4000,
      });

      // Optimize content with deduplication and chunking
      const optimizationResult = ContentOptimizer.optimizeContent(sourcesForOptimization, {
        enableDeduplication: true,
        maxChunkSize: 4000,
        chunkOverlap: 200,
        minChunkSize: 100,
        removeEmptyLines: true,
        preserveFormatting: false
      });

      const { chunks, result } = optimizationResult;

      addToast({
        type: 'success',
        title: 'Content Optimization Complete',
        message: `Optimized ${result.originalChunks} → ${result.optimizedChunks} chunks, removed ${result.duplicatesRemoved} duplicates`,
        duration: 5000,
      });

      progressTracker.completeStep('chunk');
      progressTracker.nextStep();

      progressTracker.startStep('embed', 'Generating AI embeddings for semantic search');
      await new Promise(resolve => setTimeout(resolve, 300));
      progressTracker.completeStep('embed');
      progressTracker.nextStep();

      progressTracker.startStep('store', 'Saving processed data to knowledge base');

      // Batch processing for better performance and memory management
      const BATCH_SIZE = 5; // Process content in batches to avoid memory issues
      const contentBatches = [];

      // Split successful sources into batches
      for (let i = 0; i < successfulSources.length; i += BATCH_SIZE) {
        const batch = successfulSources.slice(i, i + BATCH_SIZE);
        contentBatches.push(batch);
      }

      // Process batches sequentially to avoid overwhelming the system
      let processedChunks = 0;
      for (let batchIndex = 0; batchIndex < contentBatches.length; batchIndex++) {
        const batch = contentBatches[batchIndex];
        const batchContent = batch.map(item => item.content).join('\n\n');

        if (controller.signal.aborted) {
          throw new Error('Operation was cancelled');
        }

        // Update progress for this batch
        const batchProgress = ((batchIndex + 1) / contentBatches.length) * 100;
        progressTracker.updateStepProgress('store', batchProgress);

        await EnhancedErrorHandler.withRetry(
          () => processAndStoreContent(
            project.id,
            batchContent,
            batch[0].source.type, // Use the first source type in the batch
            `Batch ${batchIndex + 1}/${contentBatches.length}`,
            {
              batchIndex,
              totalBatches: contentBatches.length,
              sourcesInBatch: batch.length
            }
          ),
          { maxAttempts: 2 },
          (attempt) => {
            addToast({
              type: 'info',
              title: 'Retrying Batch Storage',
              message: `Retrying batch ${batchIndex + 1} storage (attempt ${attempt + 1})`,
            });
          }
        );

        processedChunks += batch.length;

        // Small delay between batches to prevent overwhelming the system
        if (batchIndex < contentBatches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      addToast({
        type: 'info',
        title: 'Batch Processing Complete',
        message: `Processed ${processedChunks} sources in ${contentBatches.length} batches`,
        duration: 3000,
      });

      progressTracker.completeStep('store');
      progressTracker.nextStep();

      // Step 6: Finalize
      progressTracker.startStep('finalize', 'Updating project status and cleaning up');

      // Reload stats
      const updatedStats = await getProjectKnowledgeBaseStats(project.id);
      setStats(updatedStats);

      // Update project status
      const updatedProject = {
        ...project,
        status: ProjectStatus.READY,
        knowledgeBaseStats: {
          chunkCount: updatedStats.chunkCount,
          lastTrainingDate: new Date(),
        },
      };
      onProjectUpdate(updatedProject);

      progressTracker.completeStep('finalize');

      // Clear data sources after successful build
      setDataSources([]);
      setProcessingStatus('');

      // Success notification
      addToast({
        type: 'success',
        title: 'Knowledge Base Built Successfully',
        message: `Knowledge base created with ${updatedStats.chunkCount} chunks. Your chatbot is now ready!`,
        duration: 6000,
      });

    } catch (err) {
      console.error('Error building knowledge base:', err);
      
      // Enhanced error handling with actionable feedback
      const errorDetails = EnhancedErrorHandler.getKnowledgeBaseErrorMessage(
        err instanceof Error ? err : new Error(String(err)),
        'build_kb'
      );
      
      setError(errorDetails.message);
      addToast({
        type: errorDetails.type,
        title: errorDetails.title,
        message: errorDetails.message,
        action: errorDetails.action,
        duration: 8000,
      });

      // Update progress tracker to show error
      if (progressTracker.currentStep) {
        progressTracker.errorStep(progressTracker.currentStep.id, 'Operation failed');
      }
    } finally {
      setIsProcessing(false);
      setProcessingStatus('');
      setAbortController(null);
    }
  };

  const handleCancelBuild = () => {
    if (abortController) {
      abortController.abort();
      addToast({
        type: 'info',
        title: 'Operation Cancelled',
        message: 'Knowledge base building has been cancelled',
      });
    }
  };

  const handleClearKnowledgeBase = async () => {
    if (!confirm('Are you sure you want to clear the entire knowledge base? This action cannot be undone.')) {
      return;
    }

    try {
      await EnhancedErrorHandler.withRetry(
        () => clearProjectKnowledgeBase(project.id),
        { maxAttempts: 2 }
      );
      
      const updatedStats = await getProjectKnowledgeBaseStats(project.id);
      setStats(updatedStats);
      
      const updatedProject = {
        ...project,
        status: ProjectStatus.INACTIVE,
        knowledgeBaseStats: {
          chunkCount: 0,
        },
      };
      onProjectUpdate(updatedProject);
      
      addToast({
        type: 'success',
        title: 'Knowledge Base Cleared',
        message: 'All knowledge base content has been successfully removed',
      });
      
    } catch (err) {
      console.error('Error clearing knowledge base:', err);
      const errorDetails = EnhancedErrorHandler.getKnowledgeBaseErrorMessage(
        err instanceof Error ? err : new Error(String(err)),
        'clear_kb'
      );
      
      setError(errorDetails.message);
      addToast({
        type: errorDetails.type,
        title: errorDetails.title,
        message: errorDetails.message,
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Knowledge Base Statistics */}
      <div className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-4">
          Knowledge Base Status
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{stats.chunkCount}</div>
            <div className="text-sm text-text-secondary-light dark:text-slate-400">Content Chunks</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">
              {project.status === ProjectStatus.READY ? 'Ready' : 'Not Ready'}
            </div>
            <div className="text-sm text-text-secondary-light dark:text-slate-400">Status</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">
              {stats.lastTrainingDate ? stats.lastTrainingDate.toLocaleDateString() : 'Never'}
            </div>
            <div className="text-sm text-text-secondary-light dark:text-slate-400">Last Updated</div>
          </div>
        </div>
      </div>

      {/* Data Source Manager */}
      <DataSourceManager
        dataSources={dataSources}
        onDataSourcesChange={handleDataSourcesChange}
        disabled={isProcessing}
        projectId={project.id}
        enableIncrementalUpdates={true}
        showSourceAnalysis={true}
      />

      {/* Training Controls */}
      <div className="bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-4">
          Training Controls
        </h3>
        
        {error && (
          <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-lg flex items-start gap-2">
            <WarningIcon className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <p className="text-red-700 dark:text-red-400 text-sm">{error}</p>
          </div>
        )}

        {/* Enhanced Progress Tracking */}
        {isProcessing && (
          <div className="mb-4">
            <ProgressTracker
              steps={progressTracker.steps}
              currentStepId={progressTracker.currentStep?.id}
              overallProgress={progressTracker.overallProgress}
              showPercentage={true}
              showTimeEstimate={true}
              cancelable={true}
              onCancel={handleCancelBuild}
            />
          </div>
        )}

        <div className="flex gap-3 flex-wrap">
          <button
            onClick={handleBuildKnowledgeBase}
            disabled={dataSources.length === 0 || isProcessing}
            className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ProcessIcon className="w-4 h-4" />
            {isProcessing ? 'Building...' : 'Build Knowledge Base'}
          </button>
          
          {isProcessing && (
            <button
              onClick={handleCancelBuild}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
          )}
          
          {stats.chunkCount > 0 && !isProcessing && (
            <button
              onClick={handleClearKnowledgeBase}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Clear Knowledge Base
            </button>
          )}
        </div>

        {/* Helpful tips when no sources are added */}
        {dataSources.length === 0 && !isProcessing && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              💡 Getting Started
            </h4>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>• Add content sources using the tabs above (URL, Text, or File)</li>
              <li>• You can add multiple sources before building</li>
              <li>• Supported file formats: .txt and .md</li>
              <li>• URLs should start with http:// or https://</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};
