import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { VectorData } from "../types";

// Ensure the API key is available, otherwise throw an error.
if (!process.env.API_KEY) {
  throw new Error("API_KEY environment variable not set.");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

/**
 * Generates an embedding for a single piece of text.
 * @param text The text to embed.
 * @returns A promise that resolves to the embedding vector (an array of numbers).
 */
export const embedContent = async (text: string): Promise<number[]> => {
  try {
    console.log(`🔧 Generating embedding for text: "${text.substring(0, 100)}..." (${text.length} chars)`);

    const result = await ai.models.embedContent({
      model: "gemini-embedding-001", // Correct model name from Google documentation
      contents: [text],
    });

    console.log(`✅ Embedding API response received:`, {
      hasEmbeddings: !!result.embeddings,
      embeddingCount: result.embeddings?.length || 0,
      firstEmbeddingLength: result.embeddings?.[0]?.values?.length || 0
    });

    if (!result.embeddings?.[0]?.values) {
      console.error("❌ Invalid embedding response structure:", result);
      throw new Error("Invalid embedding response from API.");
    }

    const embedding = result.embeddings[0].values;
    console.log(`✅ Generated embedding: ${embedding.length} dimensions`);
    return embedding;
  } catch (error) {
    console.error("❌ Error generating embedding:", error);
    console.error("Error details:", {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : String(error)}`);
  }
};


/**
 * Processes an array of text chunks to generate embeddings for each using a single batch request for efficiency.
 * @param chunks An array of text strings.
 * @returns A promise that resolves to an array of VectorData objects.
 */
export const embedChunks = async (chunks: string[]): Promise<VectorData[]> => {
  try {
    console.log(`🚀 Starting batch embedding for ${chunks.length} chunks`);

    if (chunks.length === 0) {
      console.warn("⚠️ No chunks provided for embedding");
      return [];
    }

    // Log sample chunks for debugging
    console.log(`📝 Sample chunks:`, {
      first: chunks[0]?.substring(0, 100) + "...",
      last: chunks[chunks.length - 1]?.substring(0, 100) + "...",
      totalChunks: chunks.length,
      avgLength: Math.round(chunks.reduce((sum, chunk) => sum + chunk.length, 0) / chunks.length)
    });

    const result = await ai.models.embedContent({
      model: "gemini-embedding-001", // Correct model name from Google documentation
      contents: chunks,
    });

    console.log(`✅ Batch embedding API response:`, {
      hasEmbeddings: !!result.embeddings,
      embeddingCount: result.embeddings?.length || 0,
      expectedCount: chunks.length,
      firstEmbeddingLength: result.embeddings?.[0]?.values?.length || 0
    });

    if (!result.embeddings || result.embeddings.length !== chunks.length) {
      console.error("❌ Embedding count mismatch:", {
        expected: chunks.length,
        received: result.embeddings?.length || 0,
        response: result
      });
      throw new Error(`Embedding count mismatch: expected ${chunks.length}, got ${result.embeddings?.length || 0}`);
    }

    const vectorData = result.embeddings.map((embedding, index) => {
      if (!embedding.values || embedding.values.length === 0) {
        console.error(`❌ Empty embedding at index ${index}:`, embedding);
        throw new Error(`Empty embedding received for chunk ${index}`);
      }

      return {
        text: chunks[index],
        embedding: embedding.values,
      };
    });

    console.log(`✅ Successfully created ${vectorData.length} vector data objects`);
    console.log(`📊 Embedding stats:`, {
      totalVectors: vectorData.length,
      embeddingDimension: vectorData[0]?.embedding.length || 0,
      sampleEmbeddingValues: vectorData[0]?.embedding.slice(0, 5) || []
    });

    return vectorData;
  } catch(error) {
    console.error("❌ Error in batch embedding:", error);
    console.error("Error details:", {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      chunksCount: chunks.length
    });
    throw new Error(`Failed to process chunks for embedding: ${error instanceof Error ? error.message : String(error)}`);
  }
};


/**
 * Generates a context-aware answer using Gemini 2.5 Flash.
 * @param query The user's original question.
 * @param context A string containing the relevant context retrieved from the vector store.
 * @returns A promise that resolves to the model's generated answer as a string.
 */
export const generateAnswer = async (query: string, context: string): Promise<string> => {
  const systemInstruction = `You are an expert customer support assistant. 
Your goal is to provide helpful and accurate answers based *only* on the context provided.
If the context does not contain the answer to the question, state that you don't have enough information to answer.
Do not make up information or answer based on prior knowledge.
Provide a comprehensive and detailed answer. Explain your reasoning and quote relevant parts from the context when possible. Use a clear, easy-to-read format like bullet points or numbered lists if it helps explain things better.`;

  const prompt = `
CONTEXT:
---
${context}
---

QUESTION:
${query}
`;

  try {
     const response: GenerateContentResponse = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: prompt,
        config: {
            systemInstruction: systemInstruction,
        }
     });
     return response.text;
  } catch (error) {
    console.error("Error generating answer:", error);
    throw new Error("Failed to generate an answer from the model.");
  }
};