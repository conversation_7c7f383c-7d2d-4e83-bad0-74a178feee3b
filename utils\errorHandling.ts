import { ToastType } from '../components/Toast';

export interface ErrorDetails {
  type: ToastType;
  title: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  retryable?: boolean;
  category?: 'network' | 'validation' | 'permission' | 'processing' | 'unknown';
}

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
};

/**
 * Enhanced error handler that provides user-friendly error messages with actionable guidance
 */
export class EnhancedErrorHandler {
  private static getNetworkErrorDetails(error: Error): ErrorDetails {
    if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
      return {
        type: 'error',
        title: 'Connection Problem',
        message: 'Unable to connect to the server. Please check your internet connection and try again.',
        retryable: true,
        category: 'network',
        action: {
          label: 'Check Connection',
          onClick: () => window.open('https://www.google.com', '_blank'),
        },
      };
    }

    if (error.message.includes('timeout') || error.message.includes('TIMEOUT')) {
      return {
        type: 'warning',
        title: 'Request Timeout',
        message: 'The request is taking longer than expected. This might be due to a slow connection or server load.',
        retryable: true,
        category: 'network',
      };
    }

    if (error.message.includes('503') || error.message.includes('overloaded')) {
      return {
        type: 'warning',
        title: 'Service Temporarily Unavailable',
        message: 'Our servers are experiencing high demand. Please wait a moment and try again.',
        retryable: true,
        category: 'network',
      };
    }

    return {
      type: 'error',
      title: 'Network Error',
      message: 'A network error occurred. Please check your connection and try again.',
      retryable: true,
      category: 'network',
    };
  }

  private static getValidationErrorDetails(error: Error): ErrorDetails {
    if (error.message.includes('URL') || error.message.includes('url')) {
      return {
        type: 'error',
        title: 'Invalid URL',
        message: 'Please enter a valid URL starting with http:// or https://',
        retryable: false,
        category: 'validation',
        action: {
          label: 'URL Format Guide',
          onClick: () => alert('Valid URL format: https://example.com/page'),
        },
      };
    }

    if (error.message.includes('file') && error.message.includes('type')) {
      return {
        type: 'error',
        title: 'Unsupported File Type',
        message: 'Please upload a .txt or .md file. Other formats are not currently supported.',
        retryable: false,
        category: 'validation',
        action: {
          label: 'Supported Formats',
          onClick: () => alert('Supported formats:\n• .txt (Plain text)\n• .md (Markdown)'),
        },
      };
    }

    if (error.message.includes('empty') || error.message.includes('content')) {
      return {
        type: 'warning',
        title: 'No Content Found',
        message: 'The source appears to be empty or contains no readable content. Please check the source and try again.',
        retryable: false,
        category: 'validation',
      };
    }

    return {
      type: 'error',
      title: 'Validation Error',
      message: error.message || 'Please check your input and try again.',
      retryable: false,
      category: 'validation',
    };
  }

  private static getPermissionErrorDetails(error: Error): ErrorDetails {
    if (error.message.includes('permission') || error.message.includes('unauthorized')) {
      return {
        type: 'error',
        title: 'Permission Denied',
        message: 'You don\'t have permission to perform this action. Please sign in again or contact support.',
        retryable: false,
        category: 'permission',
        action: {
          label: 'Sign In Again',
          onClick: () => window.location.reload(),
        },
      };
    }

    if (error.message.includes('quota') || error.message.includes('limit')) {
      return {
        type: 'warning',
        title: 'Service Limit Reached',
        message: 'You\'ve reached the service limit for this operation. Please try again later or upgrade your plan.',
        retryable: true,
        category: 'permission',
      };
    }

    return {
      type: 'error',
      title: 'Access Error',
      message: 'Unable to access the requested resource. Please try again or contact support.',
      retryable: false,
      category: 'permission',
    };
  }

  private static getProcessingErrorDetails(error: Error): ErrorDetails {
    if (error.message.includes('chunk') || error.message.includes('embedding')) {
      return {
        type: 'error',
        title: 'Content Processing Failed',
        message: 'Unable to process the content for the knowledge base. The content might be too large or in an unsupported format.',
        retryable: true,
        category: 'processing',
        action: {
          label: 'Try Smaller Content',
          onClick: () => alert('Tips:\n• Break large content into smaller pieces\n• Remove special characters\n• Ensure content is in plain text format'),
        },
      };
    }

    if (error.message.includes('storage') || error.message.includes('database')) {
      return {
        type: 'error',
        title: 'Storage Error',
        message: 'Unable to save your data. This might be a temporary issue with our storage system.',
        retryable: true,
        category: 'processing',
      };
    }

    return {
      type: 'error',
      title: 'Processing Error',
      message: 'An error occurred while processing your request. Please try again.',
      retryable: true,
      category: 'processing',
    };
  }

  /**
   * Analyzes an error and returns user-friendly error details with actionable guidance
   */
  static analyzeError(error: Error | string): ErrorDetails {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    const message = errorObj.message.toLowerCase();

    // Network-related errors
    if (message.includes('fetch') || message.includes('network') || message.includes('timeout') || 
        message.includes('503') || message.includes('502') || message.includes('500')) {
      return this.getNetworkErrorDetails(errorObj);
    }

    // Validation errors
    if (message.includes('url') || message.includes('file') || message.includes('empty') || 
        message.includes('invalid') || message.includes('format')) {
      return this.getValidationErrorDetails(errorObj);
    }

    // Permission errors
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden') ||
        message.includes('quota') || message.includes('limit')) {
      return this.getPermissionErrorDetails(errorObj);
    }

    // Processing errors
    if (message.includes('chunk') || message.includes('embedding') || message.includes('storage') ||
        message.includes('database') || message.includes('process')) {
      return this.getProcessingErrorDetails(errorObj);
    }

    // Generic error
    return {
      type: 'error',
      title: 'Unexpected Error',
      message: errorObj.message || 'An unexpected error occurred. Please try again or contact support if the problem persists.',
      retryable: true,
      category: 'unknown',
    };
  }

  /**
   * Implements exponential backoff retry logic
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {},
    onRetry?: (attempt: number, error: Error) => void
  ): Promise<T> {
    const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
    let lastError: Error;

    for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === finalConfig.maxAttempts) {
          throw lastError;
        }

        const errorDetails = this.analyzeError(lastError);
        if (!errorDetails.retryable) {
          throw lastError;
        }

        onRetry?.(attempt, lastError);

        // Calculate delay with exponential backoff
        const delay = Math.min(
          finalConfig.baseDelay * Math.pow(finalConfig.backoffFactor, attempt - 1),
          finalConfig.maxDelay
        );

        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Creates a user-friendly error message for knowledge base operations
   */
  static getKnowledgeBaseErrorMessage(error: Error, context: 'add_source' | 'build_kb' | 'clear_kb'): ErrorDetails {
    const baseDetails = this.analyzeError(error);

    switch (context) {
      case 'add_source':
        return {
          ...baseDetails,
          title: `Failed to Add ${baseDetails.category === 'validation' ? 'Invalid' : ''} Source`,
          message: baseDetails.category === 'validation' 
            ? baseDetails.message 
            : `Unable to add the content source. ${baseDetails.message}`,
        };

      case 'build_kb':
        return {
          ...baseDetails,
          title: 'Knowledge Base Build Failed',
          message: `The knowledge base could not be built. ${baseDetails.message}`,
          action: baseDetails.action || {
            label: 'Troubleshooting Tips',
            onClick: () => alert('Troubleshooting:\n• Check your internet connection\n• Ensure content sources are valid\n• Try with smaller content\n• Contact support if issue persists'),
          },
        };

      case 'clear_kb':
        return {
          ...baseDetails,
          title: 'Failed to Clear Knowledge Base',
          message: `Unable to clear the knowledge base. ${baseDetails.message}`,
        };

      default:
        return baseDetails;
    }
  }
}
