/**
 * Analytics dashboard for knowledge base monitoring and insights
 */

import React, { useState, useEffect } from 'react';
import { AnalyticsService, QualityReport, UsageStatistics } from '../services/analyticsService';

export interface AnalyticsDashboardProps {
  projectId: string;
  className?: string;
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  projectId,
  className = '',
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'quality' | 'usage' | 'performance'>('overview');
  const [qualityReport, setQualityReport] = useState<QualityReport | null>(null);
  const [usageStats, setUsageStats] = useState<UsageStatistics | null>(null);
  const [monitoringData, setMonitoringData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadAnalyticsData();
    
    // Refresh data every 30 seconds
    const interval = setInterval(loadAnalyticsData, 30000);
    return () => clearInterval(interval);
  }, [projectId]);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      const [quality, usage, monitoring] = await Promise.all([
        Promise.resolve(AnalyticsService.generateQualityReport(projectId)),
        Promise.resolve(AnalyticsService.getUsageStatistics(projectId, 'week')),
        Promise.resolve(AnalyticsService.getMonitoringData(projectId)),
      ]);

      setQualityReport(quality);
      setUsageStats(usage);
      setMonitoringData(monitoring);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getScoreBackground = (score: number): string => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-600 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-slate-700 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 dark:bg-slate-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-slate-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-slate-700 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-600 ${className}`}>
      {/* Header */}
      <div className="border-b border-gray-200 dark:border-slate-600 p-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-4">
          Knowledge Base Analytics
        </h3>
        
        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-slate-700 rounded-lg p-1">
          {[
            { id: 'overview', label: 'Overview' },
            { id: 'quality', label: 'Quality' },
            { id: 'usage', label: 'Usage' },
            { id: 'performance', label: 'Performance' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-white dark:bg-slate-600 text-blue-600 dark:text-blue-400 shadow-sm'
                  : 'text-gray-600 dark:text-slate-400 hover:text-gray-900 dark:hover:text-slate-200'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Alerts */}
            {monitoringData?.alerts && monitoringData.alerts.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700 dark:text-slate-300">Alerts</h4>
                {monitoringData.alerts.map((alert: any, index: number) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg border ${
                      alert.type === 'error'
                        ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200'
                        : alert.type === 'warning'
                        ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200'
                        : 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200'
                    }`}
                  >
                    <div className="flex items-center">
                      <span className="text-sm font-medium">{alert.message}</span>
                      <span className="ml-auto text-xs opacity-75">
                        {alert.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Key Metrics */}
            {monitoringData?.currentMetrics && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {monitoringData.currentMetrics.totalSources}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-slate-400">Total Sources</div>
                </div>
                
                <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {formatNumber(monitoringData.currentMetrics.totalChunks)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-slate-400">Total Chunks</div>
                </div>
                
                <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                  <div className={`text-2xl font-bold ${getScoreColor(monitoringData.currentMetrics.averageQualityScore)}`}>
                    {monitoringData.currentMetrics.averageQualityScore}%
                  </div>
                  <div className="text-sm text-gray-600 dark:text-slate-400">Quality Score</div>
                </div>
                
                <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {formatDuration(monitoringData.currentMetrics.buildTime)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-slate-400">Last Build Time</div>
                </div>
              </div>
            )}

            {/* Recent Activity */}
            {monitoringData?.recentEvents && monitoringData.recentEvents.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-slate-300 mb-3">Recent Activity</h4>
                <div className="space-y-2">
                  {monitoringData.recentEvents.slice(0, 5).map((event: any) => (
                    <div key={event.id} className="flex items-center justify-between py-2 px-3 bg-gray-50 dark:bg-slate-700 rounded">
                      <div className="flex items-center space-x-3">
                        <div className={`w-2 h-2 rounded-full ${
                          event.eventType === 'error' ? 'bg-red-500' :
                          event.eventType === 'search' ? 'bg-blue-500' :
                          event.eventType === 'build' ? 'bg-green-500' :
                          'bg-gray-500'
                        }`} />
                        <span className="text-sm text-gray-700 dark:text-slate-300">
                          {event.eventType.replace('_', ' ').toUpperCase()}
                        </span>
                        {event.eventData.query && (
                          <span className="text-xs text-gray-500 dark:text-slate-400">
                            "{event.eventData.query}"
                          </span>
                        )}
                      </div>
                      <span className="text-xs text-gray-500 dark:text-slate-400">
                        {event.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Quality Tab */}
        {activeTab === 'quality' && qualityReport && (
          <div className="space-y-6">
            {/* Overall Score */}
            <div className="text-center">
              <div className={`text-4xl font-bold ${getScoreColor(qualityReport.overallScore)} mb-2`}>
                {qualityReport.overallScore}%
              </div>
              <div className="text-sm text-gray-600 dark:text-slate-400">Overall Quality Score</div>
              <div className="w-full bg-gray-200 dark:bg-slate-600 rounded-full h-3 mt-3">
                <div
                  className={`h-3 rounded-full ${getScoreBackground(qualityReport.overallScore)}`}
                  style={{ width: `${qualityReport.overallScore}%` }}
                />
              </div>
            </div>

            {/* Quality Breakdown */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                <h5 className="font-medium text-gray-700 dark:text-slate-300 mb-3">Content Quality</h5>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Readability</span>
                    <span className={getScoreColor(qualityReport.contentQuality.averageReadability)}>
                      {qualityReport.contentQuality.averageReadability}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Completeness</span>
                    <span className={getScoreColor(qualityReport.contentQuality.averageCompleteness)}>
                      {qualityReport.contentQuality.averageCompleteness}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Duplicates</span>
                    <span className={getScoreColor(100 - qualityReport.contentQuality.duplicatePercentage)}>
                      {qualityReport.contentQuality.duplicatePercentage}%
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                <h5 className="font-medium text-gray-700 dark:text-slate-300 mb-3">Structure Quality</h5>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Small Chunks</span>
                    <span>{qualityReport.structureQuality.chunkSizeDistribution.small}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Medium Chunks</span>
                    <span>{qualityReport.structureQuality.chunkSizeDistribution.medium}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Large Chunks</span>
                    <span>{qualityReport.structureQuality.chunkSizeDistribution.large}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Orphaned</span>
                    <span className="text-red-600 dark:text-red-400">
                      {qualityReport.structureQuality.orphanedChunks}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                <h5 className="font-medium text-gray-700 dark:text-slate-300 mb-3">Performance Quality</h5>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Build Time</span>
                    <span>{formatDuration(qualityReport.performanceQuality.averageBuildTime)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Search Time</span>
                    <span>{formatDuration(qualityReport.performanceQuality.averageSearchTime)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cache Efficiency</span>
                    <span className={getScoreColor(qualityReport.performanceQuality.cacheEfficiency)}>
                      {qualityReport.performanceQuality.cacheEfficiency}%
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Recommendations */}
            {qualityReport.contentQuality.recommendations.length > 0 && (
              <div>
                <h5 className="font-medium text-gray-700 dark:text-slate-300 mb-3">Recommendations</h5>
                <div className="space-y-2">
                  {qualityReport.contentQuality.recommendations.map((rec, index) => (
                    <div key={index} className="flex items-start space-x-2 text-sm">
                      <span className="text-blue-500 mt-1">•</span>
                      <span className="text-gray-600 dark:text-slate-400">{rec}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Usage Tab */}
        {activeTab === 'usage' && usageStats && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Search Statistics */}
              <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                <h5 className="font-medium text-gray-700 dark:text-slate-300 mb-3">Search Activity</h5>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Searches</span>
                    <span className="font-medium">{usageStats.searches.total}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Success Rate</span>
                    <span className="font-medium text-green-600 dark:text-green-400">
                      {usageStats.searches.total > 0 
                        ? Math.round((usageStats.searches.successful / usageStats.searches.total) * 100)
                        : 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Avg Response Time</span>
                    <span className="font-medium">{formatDuration(usageStats.searches.averageResponseTime)}</span>
                  </div>
                </div>
              </div>

              {/* Build Statistics */}
              <div className="bg-gray-50 dark:bg-slate-700 rounded-lg p-4">
                <h5 className="font-medium text-gray-700 dark:text-slate-300 mb-3">Build Activity</h5>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Builds</span>
                    <span className="font-medium">{usageStats.builds.total}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Success Rate</span>
                    <span className="font-medium text-green-600 dark:text-green-400">
                      {usageStats.builds.total > 0 
                        ? Math.round((usageStats.builds.successful / usageStats.builds.total) * 100)
                        : 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Avg Duration</span>
                    <span className="font-medium">{formatDuration(usageStats.builds.averageDuration)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Popular Queries */}
            {usageStats.searches.popularQueries.length > 0 && (
              <div>
                <h5 className="font-medium text-gray-700 dark:text-slate-300 mb-3">Popular Search Queries</h5>
                <div className="space-y-2">
                  {usageStats.searches.popularQueries.slice(0, 5).map((query, index) => (
                    <div key={index} className="flex items-center justify-between py-2 px-3 bg-gray-50 dark:bg-slate-700 rounded">
                      <span className="text-sm text-gray-700 dark:text-slate-300 truncate">
                        "{query.query}"
                      </span>
                      <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                        {query.count}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Performance Tab */}
        {activeTab === 'performance' && (
          <div className="space-y-6">
            <div className="text-center text-gray-500 dark:text-slate-400">
              <div className="text-4xl mb-2">📊</div>
              <div>Performance charts and detailed metrics would be displayed here</div>
              <div className="text-sm mt-2">
                This would include build time trends, memory usage graphs, and cache performance metrics
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
