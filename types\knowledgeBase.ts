/**
 * TypeScript interfaces and types for Knowledge Base functionality
 */

export enum SourceType {
  URL = 'url',
  TEXT = 'text',
  FILE = 'file',
}

export enum ProjectStatus {
  INACTIVE = 'inactive',
  READY = 'ready',
  TRAINING = 'training',
  ERROR = 'error',
}

export interface DataSource {
  id: string;
  type: SourceType;
  reference: string;
  content: string;
  addedAt: Date;
  metadata?: {
    fileSize?: number;
    mimeType?: string;
    encoding?: string;
    lastModified?: Date;
    checksum?: string;
  };
}

export interface KnowledgeBaseStats {
  chunkCount: number;
  totalTokens?: number;
  lastTrainingDate?: Date;
  averageChunkSize?: number;
  sourceCount?: number;
  sourceTypes?: Record<SourceType, number>;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  status: ProjectStatus;
  knowledgeBaseStats: KnowledgeBaseStats;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  settings?: ProjectSettings;
}

export interface ProjectSettings {
  chunkSize?: number;
  chunkOverlap?: number;
  maxTokensPerChunk?: number;
  embeddingModel?: string;
  temperature?: number;
  maxResponseTokens?: number;
  systemPrompt?: string;
}

export interface ProcessingStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error' | 'cancelled';
  progress?: number;
  startTime?: Date;
  endTime?: Date;
  error?: string;
  metadata?: Record<string, any>;
}

export interface ProcessingProgress {
  steps: ProcessingStep[];
  currentStepId?: string;
  overallProgress: number;
  startTime: Date;
  estimatedEndTime?: Date;
  isComplete: boolean;
  isCancelled: boolean;
  hasErrors: boolean;
}

export interface ErrorDetails {
  category: 'network' | 'validation' | 'permission' | 'processing' | 'unknown';
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  retryable: boolean;
  action?: {
    label: string;
    handler: () => void;
  };
  timestamp: Date;
  context?: Record<string, any>;
}

export interface ToastNotification {
  id?: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    handler: () => void;
  };
  dismissible?: boolean;
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
  metadata?: {
    size: number;
    type: string;
    encoding?: string;
    preview?: string;
  };
}

export interface ContentChunk {
  id: string;
  content: string;
  embedding?: number[];
  metadata: {
    sourceId: string;
    sourceType: SourceType;
    sourceReference: string;
    chunkIndex: number;
    tokenCount: number;
    startPosition: number;
    endPosition: number;
    createdAt: Date;
  };
}

export interface SearchResult {
  chunk: ContentChunk;
  score: number;
  relevance: 'high' | 'medium' | 'low';
}

export interface KnowledgeBaseQuery {
  query: string;
  maxResults?: number;
  minScore?: number;
  sourceTypes?: SourceType[];
  sourceIds?: string[];
  includeMetadata?: boolean;
}

export interface KnowledgeBaseResponse {
  results: SearchResult[];
  totalResults: number;
  processingTime: number;
  query: KnowledgeBaseQuery;
}

export interface BuildKnowledgeBaseOptions {
  sources: DataSource[];
  settings?: {
    chunkSize?: number;
    chunkOverlap?: number;
    batchSize?: number;
    parallelProcessing?: boolean;
    validateContent?: boolean;
    deduplicateContent?: boolean;
  };
  onProgress?: (progress: ProcessingProgress) => void;
  onError?: (error: ErrorDetails) => void;
  abortSignal?: AbortSignal;
}

export interface KnowledgeBaseOperationResult {
  success: boolean;
  stats?: KnowledgeBaseStats;
  errors?: ErrorDetails[];
  warnings?: string[];
  processingTime: number;
  chunksProcessed?: number;
  sourcesProcessed?: number;
}

// Component Props Interfaces
export interface KnowledgeBaseTabProps {
  project: Project;
  onProjectUpdate: (project: Project) => void;
  className?: string;
}

export interface DataSourceManagerProps {
  dataSources: DataSource[];
  onDataSourcesChange: (sources: DataSource[]) => void;
  disabled?: boolean;
  className?: string;
  maxSources?: number;
  allowedSourceTypes?: SourceType[];
}

export interface ProgressTrackerProps {
  steps: ProcessingStep[];
  currentStepId?: string;
  overallProgress: number;
  showPercentage?: boolean;
  showTimeEstimate?: boolean;
  cancelable?: boolean;
  onCancel?: () => void;
  className?: string;
}

export interface DragDropZoneProps {
  onFilesAdded: (files: FileWithPreview[]) => void;
  onFileRemoved: (fileId: string) => void;
  acceptedTypes?: string[];
  maxFileSize?: number;
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
  showPreview?: boolean;
  multiple?: boolean;
}

export interface FileWithPreview {
  file: File;
  id: string;
  preview?: string;
  error?: string;
  processing?: boolean;
  validationResult?: FileValidationResult;
}

export interface ToastContextValue {
  toasts: ToastNotification[];
  addToast: (toast: Omit<ToastNotification, 'id'>) => void;
  removeToast: (id: string) => void;
  clearToasts: () => void;
}

// Service Interfaces
export interface KnowledgeBaseService {
  buildKnowledgeBase(
    projectId: string,
    options: BuildKnowledgeBaseOptions
  ): Promise<KnowledgeBaseOperationResult>;
  
  clearKnowledgeBase(projectId: string): Promise<KnowledgeBaseOperationResult>;
  
  getStats(projectId: string): Promise<KnowledgeBaseStats>;
  
  searchKnowledgeBase(
    projectId: string,
    query: KnowledgeBaseQuery
  ): Promise<KnowledgeBaseResponse>;
  
  validateDataSource(source: DataSource): Promise<FileValidationResult>;
  
  processContent(
    content: string,
    sourceType: SourceType,
    options?: {
      chunkSize?: number;
      chunkOverlap?: number;
      validateContent?: boolean;
    }
  ): Promise<ContentChunk[]>;
}

export interface ErrorHandlerService {
  analyzeError(error: Error): ErrorDetails;
  getContextualErrorMessage(error: Error, context: string): ErrorDetails;
  withRetry<T>(
    operation: () => Promise<T>,
    options?: {
      maxAttempts?: number;
      baseDelay?: number;
      maxDelay?: number;
      backoffFactor?: number;
    },
    onRetry?: (attempt: number, error: Error) => void
  ): Promise<T>;
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Event Types
export interface KnowledgeBaseEvent {
  type: 'build_started' | 'build_completed' | 'build_failed' | 'source_added' | 'source_removed' | 'progress_updated';
  projectId: string;
  timestamp: Date;
  data?: any;
}

export type KnowledgeBaseEventHandler = (event: KnowledgeBaseEvent) => void;

// Configuration Types
export interface KnowledgeBaseConfig {
  defaultChunkSize: number;
  defaultChunkOverlap: number;
  maxFileSize: number;
  maxSources: number;
  supportedFileTypes: string[];
  embeddingModel: string;
  retryAttempts: number;
  timeoutMs: number;
}

// Validation Types
export interface ValidationRule<T = any> {
  name: string;
  validate: (value: T) => boolean | string;
  message?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface Validator<T = any> {
  rules: ValidationRule<T>[];
  validate: (value: T) => ValidationResult;
}
