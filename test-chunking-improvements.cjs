/**
 * Simple test script to validate chunking improvements without requiring API keys
 * Run with: node test-chunking-improvements.cjs
 */

const fs = require('fs');
const path = require('path');

// Simple implementation of the chunking functions for testing
function chunkTextOld(text, chunkSize = 1000, overlapSize = 200) {
  if (overlapSize >= chunkSize) {
    throw new Error("Overlap size must be smaller than chunk size.");
  }

  // Old destructive preprocessing
  const processedText = text.replace(/(\r\n|\n|\r)/gm, " ").replace(/\s+/g, ' ').trim();

  if (processedText.length <= chunkSize) {
    return [processedText];
  }

  const chunks = [];
  let startIndex = 0;

  while (startIndex < processedText.length) {
    let endIndex = startIndex + chunkSize;

    if (endIndex > processedText.length) {
      endIndex = processedText.length;
    } else {
      const lastSpaceIndex = processedText.lastIndexOf(' ', endIndex);
      if (lastSpaceIndex > startIndex) {
        endIndex = lastSpaceIndex;
      }
    }
    
    const chunk = processedText.substring(startIndex, endIndex).trim();
    if (chunk) {
      chunks.push(chunk);
    }

    const nextStartIndex = startIndex + chunkSize - overlapSize;

    if (nextStartIndex <= startIndex) {
        startIndex = endIndex;
    } else {
        startIndex = nextStartIndex;
    }
  }

  return chunks;
}

function cleanTextPreservingStructure(text) {
  return text
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n')
    .replace(/^(#{1,6})\s+(.+)$/gm, '$1 $2')
    .replace(/^(\s*[-*+])\s+(.+)$/gm, '$1 $2')
    .replace(/^(\s*\d+\.)\s+(.+)$/gm, '$1 $2')
    .replace(/\*\*(.+?)\*\*/g, '**$1**')
    .replace(/\n{3,}/g, '\n\n')
    .replace(/[ \t]+/g, ' ')
    .trim();
}

function splitIntoSections(text) {
  const sections = [];
  const lines = text.split('\n');
  let currentSection = '';
  let currentHeader;
  let sectionStart = 0;
  let currentPosition = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);
    
    if (headerMatch && currentSection.trim()) {
      sections.push({
        content: currentSection.trim(),
        header: currentHeader,
        startPosition: sectionStart,
        endPosition: currentPosition
      });
      
      currentSection = line + '\n';
      currentHeader = headerMatch[2];
      sectionStart = currentPosition;
    } else {
      currentSection += line + '\n';
    }
    
    currentPosition += line.length + 1;
  }
  
  if (currentSection.trim()) {
    sections.push({
      content: currentSection.trim(),
      header: currentHeader,
      startPosition: sectionStart,
      endPosition: currentPosition
    });
  }
  
  return sections;
}

function createStructuralChunks(text, options) {
  const { maxChunkSize, minChunkSize, overlapSize } = options;
  const chunks = [];
  
  const sections = splitIntoSections(text);
  
  for (const section of sections) {
    if (section.content.length <= maxChunkSize) {
      chunks.push({
        content: section.content.trim(),
        metadata: {
          index: chunks.length,
          startPosition: section.startPosition,
          endPosition: section.endPosition,
          sectionHeader: section.header,
          hasStructure: true
        }
      });
    } else {
      // For simplicity, just split large sections by paragraphs
      const paragraphs = section.content.split(/\n\s*\n/);
      let currentChunk = section.header ? `${section.header}\n\n` : '';
      
      for (const paragraph of paragraphs) {
        if ((currentChunk + paragraph).length <= maxChunkSize) {
          currentChunk += paragraph + '\n\n';
        } else {
          if (currentChunk.trim().length >= minChunkSize) {
            chunks.push({
              content: currentChunk.trim(),
              metadata: {
                index: chunks.length,
                sectionHeader: section.header,
                hasStructure: true
              }
            });
          }
          currentChunk = paragraph + '\n\n';
        }
      }
      
      if (currentChunk.trim().length >= minChunkSize) {
        chunks.push({
          content: currentChunk.trim(),
          metadata: {
            index: chunks.length,
            sectionHeader: section.header,
            hasStructure: true
          }
        });
      }
    }
  }
  
  return chunks;
}

function semanticChunkTextNew(text, options = {}) {
  const {
    maxChunkSize = 500,
    minChunkSize = 100,
    overlapSize = 100,
    preserveStructure = true
  } = options;

  const cleanedText = preserveStructure ? 
    cleanTextPreservingStructure(text) : 
    text.replace(/(\r\n|\n|\r)/gm, " ").replace(/\s+/g, ' ').trim();

  if (cleanedText.length <= maxChunkSize) {
    return [{
      content: cleanedText,
      metadata: {
        index: 0,
        startPosition: 0,
        endPosition: cleanedText.length,
        hasStructure: preserveStructure
      }
    }];
  }

  if (preserveStructure) {
    return createStructuralChunks(cleanedText, { maxChunkSize, minChunkSize, overlapSize });
  }
}

async function testChunkingImprovements() {
  console.log('🧪 Testing RAG Chunking Improvements\n');
  
  try {
    // Load the test document
    const docPath = path.join(__dirname, '00_Docs', 'RAG_eComEasy.md');
    const documentContent = fs.readFileSync(docPath, 'utf-8');
    console.log(`📄 Loaded document: ${documentContent.length} characters\n`);

    // Test old chunking strategy
    console.log('📊 Old Chunking Strategy (1000 chars, 200 overlap):');
    const oldChunks = chunkTextOld(documentContent, 1000, 200);
    console.log(`   Created ${oldChunks.length} chunks`);
    console.log(`   Average chunk size: ${Math.round(oldChunks.reduce((sum, chunk) => sum + chunk.length, 0) / oldChunks.length)} chars`);

    // Test new semantic chunking
    console.log('\n📊 New Semantic Chunking Strategy (500 chars, 100 overlap):');
    const newChunks = semanticChunkTextNew(documentContent, {
      maxChunkSize: 500,
      minChunkSize: 100,
      overlapSize: 100,
      preserveStructure: true
    });
    console.log(`   Created ${newChunks.length} chunks`);
    console.log(`   Average chunk size: ${Math.round(newChunks.reduce((sum, chunk) => sum + chunk.content.length, 0) / newChunks.length)} chars`);

    // Test specific content retrieval
    console.log('\n🔍 Testing Specific Content Retrieval:');
    
    const testCases = [
      { keyword: '<EMAIL>', description: 'Support email address' },
      { keyword: '30-day money-back', description: 'Refund policy' },
      { keyword: 'Paddle', description: 'Payment methods' },
      { keyword: 'SSLCOMMERZ', description: 'Payment methods (Bangladesh)' }
    ];

    for (const testCase of testCases) {
      console.log(`\n   Testing: ${testCase.description} (${testCase.keyword})`);
      
      // Old strategy
      const oldMatches = oldChunks.filter(chunk => 
        chunk.toLowerCase().includes(testCase.keyword.toLowerCase())
      );
      console.log(`   📊 Old strategy: ${oldMatches.length} chunks contain "${testCase.keyword}"`);
      if (oldMatches.length > 0) {
        const avgSize = Math.round(oldMatches.reduce((sum, chunk) => sum + chunk.length, 0) / oldMatches.length);
        console.log(`      Average matching chunk size: ${avgSize} chars`);
        console.log(`      Sample: "${oldMatches[0].substring(0, 150)}..."`);
      }

      // New strategy
      const newMatches = newChunks.filter(chunk => 
        chunk.content.toLowerCase().includes(testCase.keyword.toLowerCase())
      );
      console.log(`   📊 New strategy: ${newMatches.length} chunks contain "${testCase.keyword}"`);
      if (newMatches.length > 0) {
        const avgSize = Math.round(newMatches.reduce((sum, chunk) => sum + chunk.content.length, 0) / newMatches.length);
        console.log(`      Average matching chunk size: ${avgSize} chars`);
        console.log(`      Sample: "${newMatches[0].content.substring(0, 150)}..."`);
        if (newMatches[0].metadata.sectionHeader) {
          console.log(`      Section: ${newMatches[0].metadata.sectionHeader}`);
        }
      }
    }

    console.log('\n✅ Chunking test completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   • Old strategy: ${oldChunks.length} large chunks (avg ${Math.round(oldChunks.reduce((sum, chunk) => sum + chunk.length, 0) / oldChunks.length)} chars)`);
    console.log(`   • New strategy: ${newChunks.length} focused chunks (avg ${Math.round(newChunks.reduce((sum, chunk) => sum + chunk.content.length, 0) / newChunks.length)} chars)`);
    console.log('   • New strategy preserves document structure and creates more focused chunks');
    console.log('   • This should improve semantic matching for specific queries');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testChunkingImprovements();
