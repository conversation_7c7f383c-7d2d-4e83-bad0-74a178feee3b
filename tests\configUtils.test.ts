import { ensureValidProjectConfig, getSafeArrayFromConfig, validateSuggestedQuestions, DEFAULT_PROJECT_CONFIG } from '../utils/configUtils';
import { ProjectConfig } from '../types';

describe('configUtils', () => {
  describe('ensureValidProjectConfig', () => {
    it('should return default config when input is null', () => {
      const result = ensureValidProjectConfig(null);
      expect(result).toEqual(DEFAULT_PROJECT_CONFIG);
    });

    it('should return default config when input is undefined', () => {
      const result = ensureValidProjectConfig(undefined);
      expect(result).toEqual(DEFAULT_PROJECT_CONFIG);
    });

    it('should return default config when input is not an object', () => {
      const result = ensureValidProjectConfig('invalid' as any);
      expect(result).toEqual(DEFAULT_PROJECT_CONFIG);
    });

    it('should merge partial config with defaults', () => {
      const partialConfig: Partial<ProjectConfig> = {
        themeColor: '#ff0000',
        welcomeMessage: 'Custom welcome'
      };
      
      const result = ensureValidProjectConfig(partialConfig);
      
      expect(result.themeColor).toBe('#ff0000');
      expect(result.welcomeMessage).toBe('Custom welcome');
      expect(result.suggestedQuestions).toEqual(DEFAULT_PROJECT_CONFIG.suggestedQuestions);
      expect(result.systemPrompt).toBe(DEFAULT_PROJECT_CONFIG.systemPrompt);
    });

    it('should handle invalid suggestedQuestions field', () => {
      const invalidConfig = {
        themeColor: '#ff0000',
        welcomeMessage: 'Test',
        suggestedQuestions: 'not an array' as any,
        systemPrompt: 'Test prompt'
      };
      
      const result = ensureValidProjectConfig(invalidConfig);
      
      expect(result.suggestedQuestions).toEqual(DEFAULT_PROJECT_CONFIG.suggestedQuestions);
    });
  });

  describe('getSafeArrayFromConfig', () => {
    it('should return the array when input is valid', () => {
      const input = ['question1', 'question2'];
      const result = getSafeArrayFromConfig(input);
      expect(result).toEqual(input);
    });

    it('should return fallback when input is null', () => {
      const fallback = ['fallback'];
      const result = getSafeArrayFromConfig(null, fallback);
      expect(result).toEqual(fallback);
    });

    it('should return fallback when input is undefined', () => {
      const fallback = ['fallback'];
      const result = getSafeArrayFromConfig(undefined, fallback);
      expect(result).toEqual(fallback);
    });

    it('should return empty array when no fallback provided', () => {
      const result = getSafeArrayFromConfig(null);
      expect(result).toEqual([]);
    });
  });

  describe('validateSuggestedQuestions', () => {
    it('should return valid questions array', () => {
      const input = ['Question 1', 'Question 2', '  Question 3  '];
      const result = validateSuggestedQuestions(input);
      expect(result).toEqual(['Question 1', 'Question 2', '  Question 3  ']);
    });

    it('should filter out empty strings', () => {
      const input = ['Question 1', '', '   ', 'Question 2'];
      const result = validateSuggestedQuestions(input);
      expect(result).toEqual(['Question 1', 'Question 2']);
    });

    it('should limit to 4 questions max', () => {
      const input = ['Q1', 'Q2', 'Q3', 'Q4', 'Q5', 'Q6'];
      const result = validateSuggestedQuestions(input);
      expect(result).toHaveLength(4);
      expect(result).toEqual(['Q1', 'Q2', 'Q3', 'Q4']);
    });

    it('should return default when input is not an array', () => {
      const result = validateSuggestedQuestions('not an array');
      expect(result).toEqual(DEFAULT_PROJECT_CONFIG.suggestedQuestions);
    });

    it('should filter out non-string values', () => {
      const input = ['Question 1', 123, null, 'Question 2', undefined] as any;
      const result = validateSuggestedQuestions(input);
      expect(result).toEqual(['Question 1', 'Question 2']);
    });
  });
});
