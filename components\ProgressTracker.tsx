import React, { useState, useEffect } from 'react';

export interface ProgressStep {
  id: string;
  label: string;
  status: 'pending' | 'active' | 'completed' | 'error';
  description?: string;
  progress?: number; // 0-100 for individual step progress
}

export interface ProgressTrackerProps {
  steps: ProgressStep[];
  currentStepId?: string;
  overallProgress?: number; // 0-100 for overall progress
  showPercentage?: boolean;
  showTimeEstimate?: boolean;
  estimatedTimeRemaining?: number; // in seconds
  onCancel?: () => void;
  cancelable?: boolean;
  className?: string;
}

export const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  steps,
  currentStepId,
  overallProgress,
  showPercentage = true,
  showTimeEstimate = false,
  estimatedTimeRemaining,
  onCancel,
  cancelable = false,
  className = '',
}) => {
  const [startTime] = useState(Date.now());
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setElapsedTime(Date.now() - startTime);
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime]);

  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.round(seconds % 60);
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  };

  const getStepIcon = (step: ProgressStep) => {
    switch (step.status) {
      case 'completed':
        return (
          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case 'active':
        return (
          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-white animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
          </div>
        );
      case 'error':
        return (
          <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full" />
          </div>
        );
    }
  };

  const getStepTextColor = (step: ProgressStep) => {
    switch (step.status) {
      case 'completed':
        return 'text-green-700 dark:text-green-400';
      case 'active':
        return 'text-blue-700 dark:text-blue-400';
      case 'error':
        return 'text-red-700 dark:text-red-400';
      default:
        return 'text-gray-500 dark:text-gray-400';
    }
  };

  const activeStep = steps.find(step => step.status === 'active');
  const completedSteps = steps.filter(step => step.status === 'completed').length;
  const totalSteps = steps.length;
  const calculatedProgress = overallProgress ?? (completedSteps / totalSteps) * 100;

  return (
    <div className={`bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 ${className}`}>
      {/* Header with progress bar */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200">
            Processing Knowledge Base
          </h4>
          {showPercentage && (
            <span className="text-sm text-blue-600 dark:text-blue-400">
              {Math.round(calculatedProgress)}%
            </span>
          )}
        </div>
        
        {/* Progress bar */}
        <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
          <div
            className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${calculatedProgress}%` }}
          />
        </div>

        {/* Time information */}
        <div className="flex items-center justify-between mt-2 text-xs text-blue-600 dark:text-blue-400">
          <span>Elapsed: {formatTime(elapsedTime / 1000)}</span>
          {showTimeEstimate && estimatedTimeRemaining && (
            <span>Remaining: ~{formatTime(estimatedTimeRemaining)}</span>
          )}
        </div>
      </div>

      {/* Steps list */}
      <div className="space-y-3">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-start gap-3">
            {getStepIcon(step)}
            <div className="flex-1 min-w-0">
              <div className={`text-sm font-medium ${getStepTextColor(step)}`}>
                {step.label}
              </div>
              {step.description && (
                <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  {step.description}
                </div>
              )}
              {step.status === 'active' && step.progress !== undefined && (
                <div className="mt-2">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                    <div
                      className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                      style={{ width: `${step.progress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
            {/* Step connector line */}
            {index < steps.length - 1 && (
              <div className="absolute left-3 mt-8 w-px h-6 bg-gray-300 dark:bg-gray-600" />
            )}
          </div>
        ))}
      </div>

      {/* Current step details */}
      {activeStep && activeStep.description && (
        <div className="mt-4 p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
          <div className="text-sm text-blue-800 dark:text-blue-200">
            <strong>Current:</strong> {activeStep.description}
          </div>
        </div>
      )}

      {/* Cancel button */}
      {cancelable && onCancel && (
        <div className="mt-4 flex justify-end">
          <button
            onClick={onCancel}
            className="px-3 py-1 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 transition-colors"
          >
            Cancel Operation
          </button>
        </div>
      )}
    </div>
  );
};

// Utility hook for managing progress steps
export const useProgressTracker = (initialSteps: Omit<ProgressStep, 'status'>[]) => {
  const [steps, setSteps] = useState<ProgressStep[]>(
    initialSteps.map(step => ({ ...step, status: 'pending' as const }))
  );
  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  const updateStep = (stepId: string, updates: Partial<ProgressStep>) => {
    setSteps(prev => prev.map(step =>
      step.id === stepId ? { ...step, ...updates } : step
    ));
  };

  const updateStepProgress = (stepId: string, progress: number) => {
    updateStep(stepId, { progress });
  };

  const startStep = (stepId: string, description?: string) => {
    updateStep(stepId, { status: 'active', description });
  };

  const completeStep = (stepId: string) => {
    updateStep(stepId, { status: 'completed', progress: 100 });
  };

  const errorStep = (stepId: string, description?: string) => {
    updateStep(stepId, { status: 'error', description });
  };

  const nextStep = () => {
    if (currentStepIndex < steps.length - 1) {
      const currentStep = steps[currentStepIndex];
      completeStep(currentStep.id);
      setCurrentStepIndex(prev => prev + 1);
      const nextStep = steps[currentStepIndex + 1];
      if (nextStep) {
        startStep(nextStep.id);
      }
    }
  };

  const reset = () => {
    setSteps(prev => prev.map(step => ({ ...step, status: 'pending' as const, progress: undefined })));
    setCurrentStepIndex(0);
  };

  const getCurrentStep = () => steps[currentStepIndex];
  const getOverallProgress = () => (steps.filter(s => s.status === 'completed').length / steps.length) * 100;

  return {
    steps,
    currentStep: getCurrentStep(),
    overallProgress: getOverallProgress(),
    updateStep,
    updateStepProgress,
    startStep,
    completeStep,
    errorStep,
    nextStep,
    reset,
  };
};
