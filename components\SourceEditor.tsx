/**
 * Advanced source editor with validation and quality scoring
 */

import React, { useState, useEffect } from 'react';
import { DataSource, SourceType } from '../types/knowledgeBase';
import { useToast } from './Toast';
import { ContentOptimizer } from '../utils/contentOptimization';
import { fileProcessor } from '../services/fileProcessorService';

export interface SourceEditorProps {
  source: DataSource;
  onSave: (updatedSource: DataSource) => void;
  onCancel: () => void;
  disabled?: boolean;
}

export interface ContentQualityScore {
  overall: number; // 0-100
  readability: number;
  structure: number;
  completeness: number;
  uniqueness: number;
  suggestions: string[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  qualityScore: ContentQualityScore;
}

export const SourceEditor: React.FC<SourceEditorProps> = ({
  source,
  onSave,
  onCancel,
  disabled = false,
}) => {
  const [editedSource, setEditedSource] = useState<DataSource>(source);
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const { addToast } = useToast();

  // Track changes
  useEffect(() => {
    const hasContentChanges = editedSource.content !== source.content;
    const hasReferenceChanges = editedSource.reference !== source.reference;
    setHasChanges(hasContentChanges || hasReferenceChanges);
  }, [editedSource, source]);

  // Validate content when it changes
  useEffect(() => {
    if (editedSource.content && editedSource.content.trim().length > 0) {
      validateContent();
    }
  }, [editedSource.content]);

  const validateContent = async () => {
    if (!editedSource.content) return;

    setIsValidating(true);
    try {
      const contentValidation = ContentOptimizer.validateContent(editedSource.content);
      const qualityScore = calculateQualityScore(editedSource.content);

      setValidation({
        isValid: contentValidation.isValid,
        errors: contentValidation.errors,
        warnings: contentValidation.warnings,
        qualityScore,
      });
    } catch (error) {
      console.error('Validation error:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const calculateQualityScore = (content: string): ContentQualityScore => {
    const words = content.split(/\s+/).filter(word => word.length > 0);
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    
    // Readability score (based on average sentence length)
    const avgSentenceLength = words.length / Math.max(sentences.length, 1);
    const readability = Math.max(0, Math.min(100, 100 - (avgSentenceLength - 15) * 2));
    
    // Structure score (based on paragraph distribution)
    const avgParagraphLength = words.length / Math.max(paragraphs.length, 1);
    const structure = Math.max(0, Math.min(100, 100 - Math.abs(avgParagraphLength - 50)));
    
    // Completeness score (based on content length)
    const completeness = Math.min(100, (content.length / 1000) * 20);
    
    // Uniqueness score (placeholder - would need comparison with existing content)
    const uniqueness = 85; // Assume good uniqueness for now
    
    const overall = (readability + structure + completeness + uniqueness) / 4;
    
    const suggestions: string[] = [];
    if (readability < 70) suggestions.push('Consider shorter sentences for better readability');
    if (structure < 70) suggestions.push('Consider breaking content into more balanced paragraphs');
    if (completeness < 50) suggestions.push('Content might be too brief - consider adding more detail');
    if (words.length < 50) suggestions.push('Very short content - ensure it provides sufficient information');
    
    return {
      overall: Math.round(overall),
      readability: Math.round(readability),
      structure: Math.round(structure),
      completeness: Math.round(completeness),
      uniqueness: Math.round(uniqueness),
      suggestions,
    };
  };

  const handleSave = async () => {
    if (!validation?.isValid) {
      addToast({
        type: 'error',
        title: 'Validation Failed',
        message: 'Please fix validation errors before saving',
      });
      return;
    }

    try {
      const updatedSource: DataSource = {
        ...editedSource,
        metadata: {
          ...editedSource.metadata,
          lastModified: new Date(),
          qualityScore: validation.qualityScore.overall,
          wordCount: editedSource.content?.split(/\s+/).filter(word => word.length > 0).length || 0,
          version: (editedSource.metadata?.version || 0) + 1,
        },
      };

      onSave(updatedSource);
      
      addToast({
        type: 'success',
        title: 'Source Updated',
        message: 'Source has been successfully updated',
      });
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Save Failed',
        message: error instanceof Error ? error.message : 'Failed to save source',
      });
    }
  };

  const getQualityColor = (score: number): string => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getQualityLabel = (score: number): string => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Poor';
  };

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-600 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-text-light dark:text-slate-200">
          Edit Source
        </h3>
        <div className="flex gap-2">
          <button
            onClick={onCancel}
            disabled={disabled}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-slate-300 bg-gray-100 dark:bg-slate-700 border border-gray-300 dark:border-slate-600 rounded-md hover:bg-gray-200 dark:hover:bg-slate-600 disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={disabled || !hasChanges || !validation?.isValid}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Save Changes
          </button>
        </div>
      </div>

      {/* Source Reference */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-2">
          Source Reference
        </label>
        <input
          type="text"
          value={editedSource.reference}
          onChange={(e) => setEditedSource({ ...editedSource, reference: e.target.value })}
          disabled={disabled || editedSource.type === SourceType.FILE}
          className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-slate-200 disabled:bg-gray-100 dark:disabled:bg-slate-800 disabled:cursor-not-allowed"
          placeholder="Enter source reference (URL, file name, etc.)"
        />
        {editedSource.type === SourceType.FILE && (
          <p className="mt-1 text-xs text-gray-500 dark:text-slate-400">
            File references cannot be edited
          </p>
        )}
      </div>

      {/* Content Editor */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-slate-300 mb-2">
          Content
        </label>
        <textarea
          value={editedSource.content || ''}
          onChange={(e) => setEditedSource({ ...editedSource, content: e.target.value })}
          disabled={disabled}
          rows={12}
          className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-slate-200 disabled:opacity-50"
          placeholder="Enter or edit the source content..."
        />
        <div className="mt-2 flex justify-between text-xs text-gray-500 dark:text-slate-400">
          <span>
            {editedSource.content?.length || 0} characters, {' '}
            {editedSource.content?.split(/\s+/).filter(word => word.length > 0).length || 0} words
          </span>
          {isValidating && <span>Validating...</span>}
        </div>
      </div>

      {/* Validation Results */}
      {validation && (
        <div className="mb-4 p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 dark:text-slate-300 mb-3">
            Content Quality Analysis
          </h4>
          
          {/* Overall Score */}
          <div className="mb-3">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm text-gray-600 dark:text-slate-400">Overall Quality</span>
              <span className={`text-sm font-medium ${getQualityColor(validation.qualityScore.overall)}`}>
                {validation.qualityScore.overall}% ({getQualityLabel(validation.qualityScore.overall)})
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${
                  validation.qualityScore.overall >= 80 ? 'bg-green-500' :
                  validation.qualityScore.overall >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${validation.qualityScore.overall}%` }}
              />
            </div>
          </div>

          {/* Detailed Scores */}
          <div className="grid grid-cols-2 gap-3 mb-3">
            {[
              { label: 'Readability', score: validation.qualityScore.readability },
              { label: 'Structure', score: validation.qualityScore.structure },
              { label: 'Completeness', score: validation.qualityScore.completeness },
              { label: 'Uniqueness', score: validation.qualityScore.uniqueness },
            ].map(({ label, score }) => (
              <div key={label} className="flex justify-between">
                <span className="text-xs text-gray-600 dark:text-slate-400">{label}</span>
                <span className={`text-xs font-medium ${getQualityColor(score)}`}>
                  {score}%
                </span>
              </div>
            ))}
          </div>

          {/* Suggestions */}
          {validation.qualityScore.suggestions.length > 0 && (
            <div>
              <h5 className="text-xs font-medium text-gray-700 dark:text-slate-300 mb-2">
                Suggestions for Improvement:
              </h5>
              <ul className="text-xs text-gray-600 dark:text-slate-400 space-y-1">
                {validation.qualityScore.suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    {suggestion}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Errors and Warnings */}
          {(validation.errors.length > 0 || validation.warnings.length > 0) && (
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-slate-600">
              {validation.errors.length > 0 && (
                <div className="mb-2">
                  <h5 className="text-xs font-medium text-red-600 dark:text-red-400 mb-1">
                    Errors:
                  </h5>
                  <ul className="text-xs text-red-600 dark:text-red-400 space-y-1">
                    {validation.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {validation.warnings.length > 0 && (
                <div>
                  <h5 className="text-xs font-medium text-yellow-600 dark:text-yellow-400 mb-1">
                    Warnings:
                  </h5>
                  <ul className="text-xs text-yellow-600 dark:text-yellow-400 space-y-1">
                    {validation.warnings.map((warning, index) => (
                      <li key={index}>• {warning}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Source Metadata */}
      {editedSource.metadata && (
        <div className="text-xs text-gray-500 dark:text-slate-400 space-y-1">
          <div>Added: {editedSource.addedAt.toLocaleString()}</div>
          {editedSource.metadata.lastModified && (
            <div>Last Modified: {editedSource.metadata.lastModified.toLocaleString()}</div>
          )}
          {editedSource.metadata.version && (
            <div>Version: {editedSource.metadata.version}</div>
          )}
          {editedSource.metadata.fileSize && (
            <div>File Size: {(editedSource.metadata.fileSize / 1024).toFixed(1)} KB</div>
          )}
        </div>
      )}
    </div>
  );
};
