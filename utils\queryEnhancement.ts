/**
 * Query enhancement utilities for improving RAG retrieval performance.
 * Expands user queries with synonyms, context, and alternative phrasings.
 */

export interface QueryEnhancementOptions {
  includeSynonyms?: boolean;
  includeContext?: boolean;
  includeVariations?: boolean;
  maxExpansions?: number;
}

export interface EnhancedQuery {
  original: string;
  enhanced: string;
  expansions: string[];
  metadata: {
    synonymsAdded: number;
    variationsAdded: number;
    contextAdded: boolean;
  };
}

/**
 * Common synonyms and variations for customer support queries
 */
const QUERY_SYNONYMS: Record<string, string[]> = {
  // Contact information
  'support': ['customer service', 'help desk', 'assistance', 'customer support', 'technical support'],
  'email': ['email address', 'contact email', 'support email', 'e-mail'],
  'phone': ['telephone', 'phone number', 'contact number', 'call'],
  'contact': ['reach', 'get in touch', 'communicate', 'connect'],
  
  // Payment and billing
  'payment': ['billing', 'pay', 'charge', 'transaction', 'purchase'],
  'refund': ['money back', 'return', 'reimbursement', 'refund policy'],
  'price': ['cost', 'pricing', 'fee', 'rate', 'subscription'],
  'plan': ['subscription', 'package', 'tier', 'pricing plan'],
  
  // Service and features
  'service': ['product', 'platform', 'system', 'tool', 'application'],
  'feature': ['capability', 'function', 'functionality', 'option'],
  'how': ['what is', 'explain', 'describe', 'tell me about'],
  
  // Common question words
  'what': ['which', 'tell me', 'explain', 'describe'],
  'how_to': ['what is the process', 'what are the steps', 'explain how'],
  'where': ['what is the location', 'find'],
  'when': ['what time', 'schedule', 'hours'],
};

/**
 * Context patterns for different types of queries
 */
const CONTEXT_PATTERNS: Record<string, string> = {
  email: 'contact information support email address',
  phone: 'contact information phone number telephone',
  refund: 'refund policy money back guarantee return',
  payment: 'payment methods billing pricing subscription',
  support: 'customer support help assistance contact',
  hours: 'business hours support hours availability',
  address: 'office address location headquarters',
  plan: 'subscription plans pricing tiers packages',
  feature: 'features capabilities functionality',
};

/**
 * Enhance a user query for better semantic matching
 */
export function enhanceQuery(
  query: string,
  options: QueryEnhancementOptions = {}
): EnhancedQuery {
  const {
    includeSynonyms = true,
    includeContext = true,
    includeVariations = true,
    maxExpansions = 10
  } = options;

  const original = query.toLowerCase().trim();
  const expansions: string[] = [];
  let synonymsAdded = 0;
  let variationsAdded = 0;
  let contextAdded = false;

  // Start with the original query
  let enhanced = original;

  // Add synonyms
  if (includeSynonyms) {
    const words = original.split(/\s+/);
    const synonymExpansions: string[] = [];
    
    for (const word of words) {
      const synonyms = QUERY_SYNONYMS[word];
      if (synonyms) {
        synonymExpansions.push(...synonyms);
        synonymsAdded += synonyms.length;
      }
    }
    
    if (synonymExpansions.length > 0) {
      expansions.push(...synonymExpansions.slice(0, maxExpansions));
    }
  }

  // Add contextual information
  if (includeContext) {
    for (const [key, context] of Object.entries(CONTEXT_PATTERNS)) {
      if (original.includes(key)) {
        expansions.push(context);
        contextAdded = true;
        break; // Only add one context pattern
      }
    }
  }

  // Add query variations
  if (includeVariations) {
    const variations = generateQueryVariations(original);
    expansions.push(...variations);
    variationsAdded = variations.length;
  }

  // Combine original query with expansions
  if (expansions.length > 0) {
    const uniqueExpansions = [...new Set(expansions)];
    enhanced = `${original} ${uniqueExpansions.join(' ')}`;
  }

  return {
    original,
    enhanced,
    expansions,
    metadata: {
      synonymsAdded,
      variationsAdded,
      contextAdded
    }
  };
}

/**
 * Generate variations of a query for better matching
 */
function generateQueryVariations(query: string): string[] {
  const variations: string[] = [];
  
  // Remove question words and rephrase
  const withoutQuestionWords = query
    .replace(/^(what|how|where|when|why|which)\s+(is|are|do|does|can|will)\s+/i, '')
    .replace(/\?$/, '');
  
  if (withoutQuestionWords !== query) {
    variations.push(withoutQuestionWords);
  }

  // Add common question patterns
  if (query.includes('email')) {
    variations.push('email address', 'contact email', 'support email');
  }
  
  if (query.includes('refund')) {
    variations.push('refund policy', 'money back', 'return policy');
  }
  
  if (query.includes('payment')) {
    variations.push('payment methods', 'how to pay', 'billing options');
  }
  
  if (query.includes('support')) {
    variations.push('customer support', 'help', 'assistance');
  }

  // Add noun phrases
  const words = query.split(/\s+/);
  if (words.length > 1) {
    // Extract potential noun phrases
    for (let i = 0; i < words.length - 1; i++) {
      const phrase = words.slice(i, i + 2).join(' ');
      if (phrase.length > 3) {
        variations.push(phrase);
      }
    }
  }

  return variations.slice(0, 5); // Limit variations
}

/**
 * Preprocess query for better embedding generation
 */
export function preprocessQuery(query: string): string {
  return query
    .toLowerCase()
    .trim()
    // Remove excessive punctuation
    .replace(/[!?]{2,}/g, '?')
    .replace(/\.{2,}/g, '.')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    // Remove trailing punctuation for better matching
    .replace(/[.!?]+$/, '');
}

/**
 * Smart query enhancement that adapts based on query type
 */
export function smartEnhanceQuery(query: string): string {
  const preprocessed = preprocessQuery(query);
  
  // Detect query intent and enhance accordingly
  if (isContactQuery(preprocessed)) {
    return enhanceContactQuery(preprocessed);
  } else if (isPolicyQuery(preprocessed)) {
    return enhancePolicyQuery(preprocessed);
  } else if (isFeatureQuery(preprocessed)) {
    return enhanceFeatureQuery(preprocessed);
  } else {
    // General enhancement
    const enhanced = enhanceQuery(preprocessed, {
      includeSynonyms: true,
      includeContext: true,
      includeVariations: false, // Keep it focused
      maxExpansions: 5
    });
    return enhanced.enhanced;
  }
}

function isContactQuery(query: string): boolean {
  const contactKeywords = ['email', 'phone', 'contact', 'support', 'address', 'reach'];
  return contactKeywords.some(keyword => query.includes(keyword));
}

function isPolicyQuery(query: string): boolean {
  const policyKeywords = ['refund', 'policy', 'terms', 'privacy', 'billing', 'payment'];
  return policyKeywords.some(keyword => query.includes(keyword));
}

function isFeatureQuery(query: string): boolean {
  const featureKeywords = ['how', 'what', 'feature', 'capability', 'function', 'service'];
  return featureKeywords.some(keyword => query.includes(keyword));
}

function enhanceContactQuery(query: string): string {
  return `${query} contact information support email phone address customer service help`;
}

function enhancePolicyQuery(query: string): string {
  return `${query} policy terms conditions billing payment refund guarantee`;
}

function enhanceFeatureQuery(query: string): string {
  return `${query} features capabilities functionality service product platform`;
}
