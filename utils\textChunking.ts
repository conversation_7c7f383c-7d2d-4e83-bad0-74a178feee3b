/**
 * Enhanced chunking strategy that preserves document structure and creates semantically coherent chunks.
 * This approach maintains section headers, lists, and natural boundaries for better RAG performance.
 */

export interface ChunkingOptions {
  maxChunkSize?: number;
  minChunkSize?: number;
  overlapSize?: number;
  preserveStructure?: boolean;
}

export interface TextChunk {
  content: string;
  metadata: {
    index: number;
    startPosition: number;
    endPosition: number;
    sectionHeader?: string;
    hasStructure: boolean;
  };
}

/**
 * Legacy chunking function for backward compatibility.
 * @deprecated Use semanticChunkText for better results
 */
export const chunkText = (
  text: string,
  chunkSize = 1000,
  overlapSize = 200
): string[] => {
  return semanticChunkText(text, { maxChunkSize: chunkSize, overlapSize }).map(chunk => chunk.content);
};

/**
 * Advanced semantic-aware chunking that preserves document structure.
 * Creates smaller, more focused chunks for better semantic matching.
 */
export const semanticChunkText = (
  text: string,
  options: ChunkingOptions = {}
): TextChunk[] => {
  const {
    maxChunkSize = 500,  // Smaller chunks for better semantic matching
    minChunkSize = 100,
    overlapSize = 100,
    preserveStructure = true
  } = options;

  if (overlapSize >= maxChunkSize) {
    throw new Error("Overlap size must be smaller than chunk size.");
  }

  // Clean text while preserving structure
  const cleanedText = preserveStructure ?
    cleanTextPreservingStructure(text) :
    text.replace(/(\r\n|\n|\r)/gm, " ").replace(/\s+/g, ' ').trim();

  if (cleanedText.length <= maxChunkSize) {
    return [{
      content: cleanedText,
      metadata: {
        index: 0,
        startPosition: 0,
        endPosition: cleanedText.length,
        hasStructure: preserveStructure && hasDocumentStructure(cleanedText)
      }
    }];
  }

  if (preserveStructure) {
    return createStructuralChunks(cleanedText, { maxChunkSize, minChunkSize, overlapSize });
  } else {
    return createSlidingWindowChunks(cleanedText, { maxChunkSize, minChunkSize, overlapSize });
  }
};

/**
 * Clean text while preserving important structural elements
 */
function cleanTextPreservingStructure(text: string): string {
  return text
    // Normalize line endings
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n')
    // Preserve section headers (## Header)
    .replace(/^(#{1,6})\s+(.+)$/gm, '$1 $2')
    // Preserve list items
    .replace(/^(\s*[-*+])\s+(.+)$/gm, '$1 $2')
    .replace(/^(\s*\d+\.)\s+(.+)$/gm, '$1 $2')
    // Preserve important formatting
    .replace(/\*\*(.+?)\*\*/g, '**$1**')
    // Clean up excessive whitespace but preserve structure
    .replace(/\n{3,}/g, '\n\n')
    .replace(/[ \t]+/g, ' ')
    .trim();
}

/**
 * Check if text has document structure (headers, lists, etc.)
 */
function hasDocumentStructure(text: string): boolean {
  const structurePatterns = [
    /^#{1,6}\s+/m,  // Headers
    /^\s*[-*+]\s+/m,  // Bullet lists
    /^\s*\d+\.\s+/m,  // Numbered lists
    /\*\*.*?\*\*/,  // Bold text
  ];

  return structurePatterns.some(pattern => pattern.test(text));
}

/**
 * Create chunks based on document structure (sections, paragraphs, lists)
 */
function createStructuralChunks(
  text: string,
  options: Required<Pick<ChunkingOptions, 'maxChunkSize' | 'minChunkSize' | 'overlapSize'>>
): TextChunk[] {
  const { maxChunkSize, minChunkSize, overlapSize } = options;
  const chunks: TextChunk[] = [];

  // Split into sections based on headers
  const sections = splitIntoSections(text);

  for (const section of sections) {
    if (section.content.length <= maxChunkSize) {
      // Section fits in one chunk
      chunks.push({
        content: section.content.trim(),
        metadata: {
          index: chunks.length,
          startPosition: section.startPosition,
          endPosition: section.endPosition,
          sectionHeader: section.header,
          hasStructure: true
        }
      });
    } else {
      // Section needs to be split further
      const subChunks = splitLargeSection(section, { maxChunkSize, minChunkSize, overlapSize });
      chunks.push(...subChunks.map((chunk, idx) => ({
        ...chunk,
        metadata: {
          ...chunk.metadata,
          index: chunks.length + idx,
          sectionHeader: section.header
        }
      })));
    }
  }

  return chunks;
}

/**
 * Split text into sections based on headers
 */
function splitIntoSections(text: string): Array<{
  content: string;
  header?: string;
  startPosition: number;
  endPosition: number;
}> {
  const sections: Array<{
    content: string;
    header?: string;
    startPosition: number;
    endPosition: number;
  }> = [];

  const lines = text.split('\n');
  let currentSection = '';
  let currentHeader: string | undefined;
  let sectionStart = 0;
  let currentPosition = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);

    if (headerMatch && currentSection.trim()) {
      // Save previous section
      sections.push({
        content: currentSection.trim(),
        header: currentHeader,
        startPosition: sectionStart,
        endPosition: currentPosition
      });

      // Start new section
      currentSection = line + '\n';
      currentHeader = headerMatch[2];
      sectionStart = currentPosition;
    } else {
      currentSection += line + '\n';
    }

    currentPosition += line.length + 1; // +1 for newline
  }

  // Add final section
  if (currentSection.trim()) {
    sections.push({
      content: currentSection.trim(),
      header: currentHeader,
      startPosition: sectionStart,
      endPosition: currentPosition
    });
  }

  return sections;
}

/**
 * Split large sections into smaller chunks while preserving context
 */
function splitLargeSection(
  section: { content: string; header?: string; startPosition: number; endPosition: number },
  options: Required<Pick<ChunkingOptions, 'maxChunkSize' | 'minChunkSize' | 'overlapSize'>>
): TextChunk[] {
  const { maxChunkSize, minChunkSize, overlapSize } = options;
  const chunks: TextChunk[] = [];

  // Try to split on paragraphs first
  const paragraphs = section.content.split(/\n\s*\n/);
  let currentChunk = section.header ? `${section.header}\n\n` : '';
  let chunkStart = section.startPosition;

  for (const paragraph of paragraphs) {
    const paragraphWithNewlines = paragraph.trim() + '\n\n';

    if ((currentChunk + paragraphWithNewlines).length <= maxChunkSize) {
      currentChunk += paragraphWithNewlines;
    } else {
      // Save current chunk if it meets minimum size
      if (currentChunk.trim().length >= minChunkSize) {
        chunks.push({
          content: currentChunk.trim(),
          metadata: {
            index: chunks.length,
            startPosition: chunkStart,
            endPosition: chunkStart + currentChunk.length,
            hasStructure: true
          }
        });
      }

      // Start new chunk with overlap
      const overlap = getOverlapText(currentChunk, overlapSize);
      currentChunk = overlap + paragraphWithNewlines;
      chunkStart += currentChunk.length - overlap.length;
    }
  }

  // Add final chunk
  if (currentChunk.trim().length >= minChunkSize) {
    chunks.push({
      content: currentChunk.trim(),
      metadata: {
        index: chunks.length,
        startPosition: chunkStart,
        endPosition: section.endPosition,
        hasStructure: true
      }
    });
  }

  return chunks;
}
/**
 * Get overlap text from the end of a chunk
 */
function getOverlapText(text: string, overlapSize: number): string {
  if (text.length <= overlapSize) {
    return text;
  }

  const overlapText = text.slice(-overlapSize);
  // Try to start at a word boundary
  const spaceIndex = overlapText.indexOf(' ');
  if (spaceIndex > 0) {
    return overlapText.slice(spaceIndex + 1);
  }

  return overlapText;
}

/**
 * Fallback sliding window chunking for non-structured text
 */
function createSlidingWindowChunks(
  text: string,
  options: Required<Pick<ChunkingOptions, 'maxChunkSize' | 'minChunkSize' | 'overlapSize'>>
): TextChunk[] {
  const { maxChunkSize, minChunkSize, overlapSize } = options;
  const chunks: TextChunk[] = [];
  let startIndex = 0;

  while (startIndex < text.length) {
    let endIndex = startIndex + maxChunkSize;

    if (endIndex > text.length) {
      endIndex = text.length;
    } else {
      // Find the last space before the endIndex to avoid splitting a word
      const lastSpaceIndex = text.lastIndexOf(' ', endIndex);
      if (lastSpaceIndex > startIndex) {
        endIndex = lastSpaceIndex;
      }
    }

    const chunk = text.substring(startIndex, endIndex).trim();
    if (chunk.length >= minChunkSize) {
      chunks.push({
        content: chunk,
        metadata: {
          index: chunks.length,
          startPosition: startIndex,
          endPosition: endIndex,
          hasStructure: false
        }
      });
    }

    // Move the start index for the next chunk, creating an overlap
    const nextStartIndex = startIndex + maxChunkSize - overlapSize;

    // Prevent infinite loop
    if (nextStartIndex <= startIndex) {
      startIndex = endIndex;
    } else {
      startIndex = nextStartIndex;
    }
  }

  return chunks;
}