/**
 * Source versioning service for tracking changes and managing source history
 */

import { DataSource } from '../types/knowledgeBase';

export interface SourceVersion {
  id: string;
  sourceId: string;
  version: number;
  content: string;
  reference: string;
  timestamp: Date;
  changeType: 'created' | 'updated' | 'content_changed' | 'reference_changed';
  changeDescription?: string;
  contentHash: string;
  metadata: {
    wordCount: number;
    characterCount: number;
    qualityScore?: number;
    changedBy?: string;
    changeReason?: string;
  };
}

export interface VersionComparison {
  sourceId: string;
  oldVersion: SourceVersion;
  newVersion: SourceVersion;
  changes: {
    contentChanged: boolean;
    referenceChanged: boolean;
    addedLines: string[];
    removedLines: string[];
    modifiedLines: Array<{
      lineNumber: number;
      oldContent: string;
      newContent: string;
    }>;
  };
  similarity: number; // 0-100 percentage
}

export interface VersionHistory {
  sourceId: string;
  versions: SourceVersion[];
  totalVersions: number;
  firstVersion: Date;
  lastVersion: Date;
}

export class SourceVersioningService {
  private static versions = new Map<string, SourceVersion[]>();

  /**
   * Create a new version of a source
   */
  public static async createVersion(
    source: DataSource,
    changeType: SourceVersion['changeType'],
    changeDescription?: string,
    changedBy?: string,
    changeReason?: string
  ): Promise<SourceVersion> {
    const contentHash = await this.generateContentHash(source.content || '');
    const versions = this.versions.get(source.id) || [];
    const nextVersion = versions.length + 1;

    const version: SourceVersion = {
      id: `${source.id}-v${nextVersion}`,
      sourceId: source.id,
      version: nextVersion,
      content: source.content || '',
      reference: source.reference,
      timestamp: new Date(),
      changeType,
      changeDescription,
      contentHash,
      metadata: {
        wordCount: source.content?.split(/\s+/).filter(word => word.length > 0).length || 0,
        characterCount: source.content?.length || 0,
        qualityScore: source.metadata?.qualityScore,
        changedBy,
        changeReason,
      },
    };

    versions.push(version);
    this.versions.set(source.id, versions);

    return version;
  }

  /**
   * Get version history for a source
   */
  public static getVersionHistory(sourceId: string): VersionHistory | null {
    const versions = this.versions.get(sourceId);
    if (!versions || versions.length === 0) {
      return null;
    }

    return {
      sourceId,
      versions: [...versions].sort((a, b) => b.version - a.version), // Latest first
      totalVersions: versions.length,
      firstVersion: versions[0].timestamp,
      lastVersion: versions[versions.length - 1].timestamp,
    };
  }

  /**
   * Get a specific version of a source
   */
  public static getVersion(sourceId: string, version: number): SourceVersion | null {
    const versions = this.versions.get(sourceId);
    if (!versions) return null;

    return versions.find(v => v.version === version) || null;
  }

  /**
   * Get the latest version of a source
   */
  public static getLatestVersion(sourceId: string): SourceVersion | null {
    const versions = this.versions.get(sourceId);
    if (!versions || versions.length === 0) return null;

    return versions[versions.length - 1];
  }

  /**
   * Compare two versions of a source
   */
  public static compareVersions(
    sourceId: string,
    oldVersion: number,
    newVersion: number
  ): VersionComparison | null {
    const oldVer = this.getVersion(sourceId, oldVersion);
    const newVer = this.getVersion(sourceId, newVersion);

    if (!oldVer || !newVer) return null;

    const oldLines = oldVer.content.split('\n');
    const newLines = newVer.content.split('\n');

    const changes = this.calculateLineDifferences(oldLines, newLines);
    const similarity = this.calculateSimilarity(oldVer.content, newVer.content);

    return {
      sourceId,
      oldVersion: oldVer,
      newVersion: newVer,
      changes: {
        contentChanged: oldVer.contentHash !== newVer.contentHash,
        referenceChanged: oldVer.reference !== newVer.reference,
        ...changes,
      },
      similarity,
    };
  }

  /**
   * Restore a source to a previous version
   */
  public static restoreVersion(sourceId: string, version: number): DataSource | null {
    const versionData = this.getVersion(sourceId, version);
    if (!versionData) return null;

    // Create a new version for the restoration
    const restoredSource: DataSource = {
      id: sourceId,
      type: 'text' as any, // Would need to store original type
      reference: versionData.reference,
      content: versionData.content,
      addedAt: new Date(), // Original add date would be stored separately
      metadata: {
        version: versionData.version,
        restoredFrom: version,
        restoredAt: new Date(),
        qualityScore: versionData.metadata.qualityScore,
        wordCount: versionData.metadata.wordCount,
      },
    };

    return restoredSource;
  }

  /**
   * Delete old versions (keep only recent ones)
   */
  public static pruneVersions(sourceId: string, keepCount: number = 10): number {
    const versions = this.versions.get(sourceId);
    if (!versions || versions.length <= keepCount) return 0;

    const sortedVersions = [...versions].sort((a, b) => b.version - a.version);
    const versionsToKeep = sortedVersions.slice(0, keepCount);
    const deletedCount = versions.length - versionsToKeep.length;

    this.versions.set(sourceId, versionsToKeep);
    return deletedCount;
  }

  /**
   * Get version statistics
   */
  public static getVersionStats(sourceId: string): {
    totalVersions: number;
    totalChanges: number;
    averageTimeBetweenChanges: number; // in hours
    contentGrowth: number; // percentage change from first to last
    qualityTrend: 'improving' | 'declining' | 'stable' | 'unknown';
  } | null {
    const history = this.getVersionHistory(sourceId);
    if (!history || history.versions.length < 2) return null;

    const versions = history.versions.reverse(); // Oldest first for calculations
    const totalVersions = versions.length;
    const totalChanges = totalVersions - 1;

    // Calculate average time between changes
    const timeSpan = history.lastVersion.getTime() - history.firstVersion.getTime();
    const averageTimeBetweenChanges = timeSpan / (1000 * 60 * 60 * totalChanges); // hours

    // Calculate content growth
    const firstContent = versions[0].content;
    const lastContent = versions[versions.length - 1].content;
    const contentGrowth = firstContent.length > 0 
      ? ((lastContent.length - firstContent.length) / firstContent.length) * 100
      : 0;

    // Analyze quality trend
    const qualityScores = versions
      .map(v => v.metadata.qualityScore)
      .filter((score): score is number => score !== undefined);
    
    let qualityTrend: 'improving' | 'declining' | 'stable' | 'unknown' = 'unknown';
    if (qualityScores.length >= 3) {
      const firstHalf = qualityScores.slice(0, Math.floor(qualityScores.length / 2));
      const secondHalf = qualityScores.slice(Math.ceil(qualityScores.length / 2));
      
      const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
      
      const difference = secondAvg - firstAvg;
      if (Math.abs(difference) < 5) {
        qualityTrend = 'stable';
      } else if (difference > 0) {
        qualityTrend = 'improving';
      } else {
        qualityTrend = 'declining';
      }
    }

    return {
      totalVersions,
      totalChanges,
      averageTimeBetweenChanges,
      contentGrowth,
      qualityTrend,
    };
  }

  /**
   * Generate content hash for change detection
   */
  private static async generateContentHash(content: string): Promise<string> {
    // Simple hash implementation - in production, use crypto.subtle.digest
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Calculate line differences between two text versions
   */
  private static calculateLineDifferences(oldLines: string[], newLines: string[]): {
    addedLines: string[];
    removedLines: string[];
    modifiedLines: Array<{
      lineNumber: number;
      oldContent: string;
      newContent: string;
    }>;
  } {
    const addedLines: string[] = [];
    const removedLines: string[] = [];
    const modifiedLines: Array<{
      lineNumber: number;
      oldContent: string;
      newContent: string;
    }> = [];

    const maxLines = Math.max(oldLines.length, newLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const oldLine = oldLines[i];
      const newLine = newLines[i];

      if (oldLine === undefined && newLine !== undefined) {
        addedLines.push(newLine);
      } else if (oldLine !== undefined && newLine === undefined) {
        removedLines.push(oldLine);
      } else if (oldLine !== newLine) {
        modifiedLines.push({
          lineNumber: i + 1,
          oldContent: oldLine,
          newContent: newLine,
        });
      }
    }

    return { addedLines, removedLines, modifiedLines };
  }

  /**
   * Calculate similarity percentage between two texts
   */
  private static calculateSimilarity(text1: string, text2: string): number {
    if (text1 === text2) return 100;
    if (!text1 || !text2) return 0;

    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);
    
    const set1 = new Set(words1);
    const set2 = new Set(words2);
    
    const intersection = new Set([...set1].filter(word => set2.has(word)));
    const union = new Set([...set1, ...set2]);
    
    return union.size > 0 ? (intersection.size / union.size) * 100 : 0;
  }

  /**
   * Export version history for backup
   */
  public static exportVersionHistory(sourceId: string): string | null {
    const history = this.getVersionHistory(sourceId);
    if (!history) return null;

    return JSON.stringify(history, null, 2);
  }

  /**
   * Import version history from backup
   */
  public static importVersionHistory(sourceId: string, historyJson: string): boolean {
    try {
      const history: VersionHistory = JSON.parse(historyJson);
      if (history.sourceId !== sourceId) return false;

      // Convert timestamp strings back to Date objects
      const versions = history.versions.map(v => ({
        ...v,
        timestamp: new Date(v.timestamp),
      }));

      this.versions.set(sourceId, versions);
      return true;
    } catch (error) {
      console.error('Failed to import version history:', error);
      return false;
    }
  }

  /**
   * Clear all version history (use with caution)
   */
  public static clearAllVersions(): void {
    this.versions.clear();
  }

  /**
   * Get all sources with version history
   */
  public static getAllVersionedSources(): string[] {
    return Array.from(this.versions.keys());
  }
}
