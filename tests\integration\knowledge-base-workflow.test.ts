/**
 * Integration tests for the complete knowledge base workflow
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { EnhancedKnowledgeBaseService } from '../../services/enhancedKnowledgeBaseService';
import { SourceTrackingService } from '../../services/sourceTrackingService';
import { ContentOptimizer } from '../../utils/contentOptimization';
import { AnalyticsService } from '../../services/analyticsService';
import { fileProcessor } from '../../services/fileProcessorService';
import { globalCache } from '../../utils/cacheManager';
import { DataSource, SourceType } from '../../types/knowledgeBase';

// Mock external dependencies
vi.mock('../../services/projectService', () => ({
  processAndStoreContent: vi.fn().mockResolvedValue(undefined),
  getProjectKnowledgeBaseStats: vi.fn().mockResolvedValue({
    chunkCount: 10,
    sourceCount: 2,
    totalWords: 1000,
    lastUpdated: new Date(),
  }),
  clearProjectKnowledgeBase: vi.fn().mockResolvedValue(undefined),
}));

describe('Knowledge Base Workflow Integration Tests', () => {
  const testProjectId = 'test-project-123';
  
  beforeEach(() => {
    // Clear all caches and analytics data
    globalCache.clear();
    AnalyticsService.clearAllData();
    ContentOptimizer.resetMemoryTracking();
  });

  afterEach(() => {
    // Clean up after each test
    globalCache.clear();
    AnalyticsService.clearAllData();
  });

  describe('Complete Build Workflow', () => {
    it('should successfully build knowledge base with multiple sources', async () => {
      const sources: DataSource[] = [
        {
          id: 'source-1',
          type: SourceType.TEXT,
          reference: 'Test Document 1',
          content: 'This is a comprehensive test document with substantial content that should be processed correctly. It contains multiple sentences and paragraphs to test the chunking algorithm.',
          addedAt: new Date(),
        },
        {
          id: 'source-2',
          type: SourceType.TEXT,
          reference: 'Test Document 2',
          content: 'Another test document with different content. This document focuses on different topics and should create distinct chunks for better knowledge base coverage.',
          addedAt: new Date(),
        },
      ];

      const service = new EnhancedKnowledgeBaseService();
      const result = await service.buildKnowledgeBase({
        projectId: testProjectId,
        sources,
        options: {
          enableDeduplication: true,
          maxChunkSize: 1000,
          chunkOverlap: 100,
        },
      });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      
      if (result.success && result.data) {
        expect(result.data.processedSources).toBe(2);
        expect(result.data.totalChunks).toBeGreaterThan(0);
        expect(result.data.processingTime).toBeGreaterThan(0);
        expect(result.data.optimizationResult).toBeDefined();
      }
    });

    it('should handle incremental updates correctly', async () => {
      const initialSources: DataSource[] = [
        {
          id: 'source-1',
          type: SourceType.TEXT,
          reference: 'Initial Document',
          content: 'Initial content for testing incremental updates.',
          addedAt: new Date(),
        },
      ];

      const service = new EnhancedKnowledgeBaseService();
      
      // Initial build
      const initialResult = await service.buildKnowledgeBase({
        projectId: testProjectId,
        sources: initialSources,
      });

      expect(initialResult.success).toBe(true);

      // Track the initial source
      await SourceTrackingService.trackSource(
        testProjectId,
        initialSources[0],
        ['chunk-1', 'chunk-2'],
        1000
      );

      // Add new source for incremental update
      const updatedSources: DataSource[] = [
        ...initialSources,
        {
          id: 'source-2',
          type: SourceType.TEXT,
          reference: 'New Document',
          content: 'New content added for incremental update testing.',
          addedAt: new Date(),
        },
      ];

      // Incremental build (should only process new source)
      const incrementalResult = await service.buildKnowledgeBase({
        projectId: testProjectId,
        sources: updatedSources,
        options: { forceRebuild: false },
      });

      expect(incrementalResult.success).toBe(true);
      
      if (incrementalResult.success && incrementalResult.data) {
        // Should process only the new source
        expect(incrementalResult.data.processedSources).toBeLessThanOrEqual(1);
      }
    });

    it('should handle errors gracefully and provide detailed error information', async () => {
      const invalidSources: DataSource[] = [
        {
          id: 'invalid-source',
          type: SourceType.URL,
          reference: 'https://invalid-url-that-does-not-exist.com',
          content: '',
          addedAt: new Date(),
        },
      ];

      const service = new EnhancedKnowledgeBaseService();
      const result = await service.buildKnowledgeBase({
        projectId: testProjectId,
        sources: invalidSources,
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.category).toBe('network');
      expect(result.metadata?.requestId).toBeDefined();
    });
  });

  describe('Content Optimization Integration', () => {
    it('should optimize content with deduplication and memory management', async () => {
      const duplicateContent = 'This is duplicate content that should be detected and removed during optimization.';
      
      const sources = [
        {
          sourceId: 'source-1',
          sourceType: 'text',
          content: duplicateContent + ' Additional unique content for source 1.',
        },
        {
          sourceId: 'source-2',
          sourceType: 'text',
          content: duplicateContent + ' Additional unique content for source 2.',
        },
      ];

      const result = ContentOptimizer.optimizeContent(sources, {
        enableDeduplication: true,
        maxChunkSize: 500,
        chunkOverlap: 50,
      });

      expect(result.chunks.length).toBeGreaterThan(0);
      expect(result.result.duplicatesRemoved).toBeGreaterThan(0);
      expect(result.result.optimizedChunks).toBeLessThanOrEqual(result.result.originalChunks);

      // Check memory tracking
      const memoryStats = ContentOptimizer.getMemoryStats();
      expect(memoryStats.current).toBeGreaterThanOrEqual(0);
      expect(memoryStats.peak).toBeGreaterThanOrEqual(memoryStats.current);
    });

    it('should handle large content with batch processing', async () => {
      // Create large content (>50KB)
      const largeContent = 'Large content chunk. '.repeat(3000); // ~60KB
      
      const sources = [
        {
          sourceId: 'large-source',
          sourceType: 'text',
          content: largeContent,
        },
      ];

      const result = ContentOptimizer.optimizeContent(sources, {
        enableDeduplication: false,
        maxChunkSize: 4000,
        chunkOverlap: 200,
      });

      expect(result.chunks.length).toBeGreaterThan(1);
      
      // Verify batch processing metadata
      const hasGlobalIndices = result.chunks.some(chunk => 
        chunk.metadata?.globalStartIndex !== undefined
      );
      expect(hasGlobalIndices).toBe(true);
    });
  });

  describe('Caching Integration', () => {
    it('should cache and retrieve content effectively', async () => {
      const testContent = 'Test content for caching';
      const cacheKey = 'test-cache-key';

      // Set content in cache
      const setResult = globalCache.set(cacheKey, testContent, {
        ttl: 60000, // 1 minute
        tags: ['test'],
      });
      expect(setResult).toBe(true);

      // Retrieve content from cache
      const cachedContent = globalCache.get<string>(cacheKey);
      expect(cachedContent).toBe(testContent);

      // Check cache metrics
      const metrics = globalCache.getMetrics();
      expect(metrics.hitCount).toBeGreaterThan(0);
      expect(metrics.totalEntries).toBeGreaterThan(0);
    });

    it('should handle cache eviction and cleanup', async () => {
      // Fill cache with test data
      for (let i = 0; i < 10; i++) {
        globalCache.set(`test-key-${i}`, `test-content-${i}`, {
          ttl: 1000, // 1 second
          priority: i % 2 === 0 ? 'high' : 'low',
        });
      }

      const initialMetrics = globalCache.getMetrics();
      expect(initialMetrics.totalEntries).toBe(10);

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 1100));

      // Trigger cleanup by accessing cache
      globalCache.get('non-existent-key');

      // Check that expired entries were cleaned up
      const finalMetrics = globalCache.getMetrics();
      expect(finalMetrics.totalEntries).toBeLessThan(initialMetrics.totalEntries);
    });
  });

  describe('Analytics Integration', () => {
    it('should track metrics and generate reports', async () => {
      // Record test metrics
      AnalyticsService.recordMetrics({
        projectId: testProjectId,
        totalSources: 5,
        totalChunks: 50,
        totalWords: 5000,
        totalCharacters: 25000,
        averageQualityScore: 85,
        buildTime: 2500,
        searchResponseTime: 150,
        memoryUsage: 10 * 1024 * 1024, // 10MB
        cacheHitRate: 75,
        searchQueries: 20,
        successfulBuilds: 8,
        failedBuilds: 2,
        userSessions: 5,
        duplicateContent: 5,
        lowQualitySources: 1,
        orphanedChunks: 0,
        contentFreshness: 2,
      });

      // Track some events
      AnalyticsService.trackEvent({
        projectId: testProjectId,
        sessionId: 'test-session',
        eventType: 'build',
        eventData: {
          success: true,
          duration: 2500,
        },
      });

      AnalyticsService.trackEvent({
        projectId: testProjectId,
        sessionId: 'test-session',
        eventType: 'search',
        eventData: {
          query: 'test query',
          success: true,
          duration: 150,
        },
      });

      // Generate quality report
      const qualityReport = AnalyticsService.generateQualityReport(testProjectId);
      expect(qualityReport.projectId).toBe(testProjectId);
      expect(qualityReport.overallScore).toBeGreaterThan(0);
      expect(qualityReport.contentQuality.averageReadability).toBeGreaterThan(0);

      // Get usage statistics
      const usageStats = AnalyticsService.getUsageStatistics(testProjectId, 'day');
      expect(usageStats.projectId).toBe(testProjectId);
      expect(usageStats.searches.total).toBeGreaterThan(0);
      expect(usageStats.builds.total).toBeGreaterThan(0);

      // Get monitoring data
      const monitoringData = AnalyticsService.getMonitoringData(testProjectId);
      expect(monitoringData.currentMetrics).toBeDefined();
      expect(monitoringData.recentEvents.length).toBeGreaterThan(0);
    });
  });

  describe('File Processing Integration', () => {
    it('should process different file types correctly', async () => {
      // Test text file
      const textFile = new File(['Test file content'], 'test.txt', { type: 'text/plain' });
      expect(fileProcessor.isSupported(textFile)).toBe(true);

      const textResult = await fileProcessor.processFile(textFile);
      expect(textResult.content).toBe('Test file content');
      expect(textResult.metadata.format).toBe('text');
      expect(textResult.metadata.wordCount).toBe(3);

      // Test markdown file
      const markdownContent = '# Test Markdown\n\nThis is **bold** text.';
      const markdownFile = new File([markdownContent], 'test.md', { type: 'text/markdown' });
      expect(fileProcessor.isSupported(markdownFile)).toBe(true);

      const markdownResult = await fileProcessor.processFile(markdownFile);
      expect(markdownResult.content).toContain('Test Markdown');
      expect(markdownResult.metadata.format).toBe('markdown');

      // Test HTML file
      const htmlContent = '<html><body><h1>Test HTML</h1><p>This is a paragraph.</p></body></html>';
      const htmlFile = new File([htmlContent], 'test.html', { type: 'text/html' });
      expect(fileProcessor.isSupported(htmlFile)).toBe(true);

      const htmlResult = await fileProcessor.processFile(htmlFile);
      expect(htmlResult.content).toContain('Test HTML');
      expect(htmlResult.content).toContain('This is a paragraph');
      expect(htmlResult.metadata.format).toBe('html');
    });

    it('should handle file validation and size limits', async () => {
      // Test file size limit
      const largeContent = 'x'.repeat(20 * 1024 * 1024); // 20MB
      const largeFile = new File([largeContent], 'large.txt', { type: 'text/plain' });

      await expect(fileProcessor.processFile(largeFile)).rejects.toThrow(/exceeds maximum allowed size/);

      // Test unsupported file type
      const unsupportedFile = new File(['binary content'], 'test.exe', { type: 'application/octet-stream' });
      expect(fileProcessor.isSupported(unsupportedFile)).toBe(false);

      await expect(fileProcessor.processFile(unsupportedFile)).rejects.toThrow(/Unsupported file format/);
    });
  });

  describe('Source Versioning Integration', () => {
    it('should track source versions and detect changes', async () => {
      const originalSource: DataSource = {
        id: 'versioned-source',
        type: SourceType.TEXT,
        reference: 'Versioned Document',
        content: 'Original content for version tracking.',
        addedAt: new Date(),
      };

      // Create initial version
      const { SourceVersioningService } = await import('../../services/sourceVersioningService');
      const version1 = await SourceVersioningService.createVersion(
        originalSource,
        'created',
        'Initial version'
      );

      expect(version1.version).toBe(1);
      expect(version1.changeType).toBe('created');
      expect(version1.content).toBe(originalSource.content);

      // Update source and create new version
      const updatedSource: DataSource = {
        ...originalSource,
        content: 'Updated content for version tracking with additional information.',
      };

      const version2 = await SourceVersioningService.createVersion(
        updatedSource,
        'content_changed',
        'Content updated'
      );

      expect(version2.version).toBe(2);
      expect(version2.changeType).toBe('content_changed');

      // Compare versions
      const comparison = SourceVersioningService.compareVersions(originalSource.id, 1, 2);
      expect(comparison).toBeDefined();
      expect(comparison?.changes.contentChanged).toBe(true);
      expect(comparison?.similarity).toBeLessThan(100);

      // Get version history
      const history = SourceVersioningService.getVersionHistory(originalSource.id);
      expect(history).toBeDefined();
      expect(history?.totalVersions).toBe(2);
      expect(history?.versions.length).toBe(2);
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle and categorize different types of errors', async () => {
      const { EnhancedErrorHandler } = await import('../../utils/errorHandling');

      // Test network error
      const networkError = new Error('fetch failed');
      const networkResult = await EnhancedErrorHandler.handleWithRetry(
        () => Promise.reject(networkError),
        { maxAttempts: 2, baseDelay: 100 }
      );

      expect(networkResult.success).toBe(false);
      expect(networkResult.error?.category).toBe('network');
      expect(networkResult.attempts).toBe(2);

      // Test validation error
      const validationError = new Error('Invalid input provided');
      const validationResult = await EnhancedErrorHandler.handleWithRetry(
        () => Promise.reject(validationError),
        { maxAttempts: 1 }
      );

      expect(validationResult.success).toBe(false);
      expect(validationResult.error?.category).toBe('validation');
      expect(validationResult.attempts).toBe(1); // Should not retry validation errors
    });
  });

  describe('Performance Tests', () => {
    it('should handle large datasets efficiently', async () => {
      const startTime = Date.now();
      
      // Create multiple large sources
      const largeSources: DataSource[] = Array.from({ length: 10 }, (_, i) => ({
        id: `large-source-${i}`,
        type: SourceType.TEXT,
        reference: `Large Document ${i}`,
        content: 'Large content chunk. '.repeat(1000), // ~20KB each
        addedAt: new Date(),
      }));

      const service = new EnhancedKnowledgeBaseService();
      const result = await service.buildKnowledgeBase({
        projectId: testProjectId,
        sources: largeSources,
        options: {
          enableDeduplication: true,
          batchSize: 3,
        },
      });

      const processingTime = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(processingTime).toBeLessThan(30000); // Should complete within 30 seconds
      
      if (result.success && result.data) {
        expect(result.data.processedSources).toBe(10);
        expect(result.data.optimizationResult?.duplicatesRemoved).toBeGreaterThan(0);
      }

      // Check memory usage
      const memoryStats = ContentOptimizer.getMemoryStats();
      expect(memoryStats.utilizationPercentage).toBeLessThan(90); // Should not exceed 90% of limit
    });

    it('should maintain good cache performance under load', async () => {
      const iterations = 100;
      const startTime = Date.now();

      // Perform many cache operations
      for (let i = 0; i < iterations; i++) {
        const key = `perf-test-${i}`;
        const value = `test-value-${i}`;
        
        globalCache.set(key, value);
        const retrieved = globalCache.get(key);
        expect(retrieved).toBe(value);
      }

      const operationTime = Date.now() - startTime;
      const metrics = globalCache.getMetrics();

      expect(operationTime).toBeLessThan(1000); // Should complete within 1 second
      expect(metrics.hitRate).toBeGreaterThan(95); // Should have high hit rate
      expect(metrics.totalEntries).toBe(iterations);
    });
  });
});
