/**
 * Analytics and monitoring service for knowledge base operations
 */

export interface KnowledgeBaseMetrics {
  projectId: string;
  timestamp: Date;
  
  // Content metrics
  totalSources: number;
  totalChunks: number;
  totalWords: number;
  totalCharacters: number;
  averageQualityScore: number;
  
  // Performance metrics
  buildTime: number; // milliseconds
  searchResponseTime: number; // milliseconds
  memoryUsage: number; // bytes
  cacheHitRate: number; // percentage
  
  // Usage metrics
  searchQueries: number;
  successfulBuilds: number;
  failedBuilds: number;
  userSessions: number;
  
  // Quality metrics
  duplicateContent: number; // percentage
  lowQualitySources: number;
  orphanedChunks: number;
  contentFreshness: number; // days since last update
}

export interface UserBehaviorEvent {
  id: string;
  projectId: string;
  userId?: string;
  sessionId: string;
  timestamp: Date;
  eventType: 'search' | 'build' | 'source_add' | 'source_edit' | 'source_remove' | 'error';
  eventData: {
    query?: string;
    sourceType?: string;
    errorType?: string;
    duration?: number;
    success?: boolean;
    metadata?: Record<string, any>;
  };
}

export interface PerformanceMetric {
  id: string;
  projectId: string;
  timestamp: Date;
  metricType: 'build_time' | 'search_time' | 'memory_usage' | 'cache_performance';
  value: number;
  unit: string;
  metadata?: {
    sourceCount?: number;
    chunkCount?: number;
    queryLength?: number;
    cacheSize?: number;
  };
}

export interface QualityReport {
  projectId: string;
  generatedAt: Date;
  overallScore: number; // 0-100
  
  contentQuality: {
    averageReadability: number;
    averageCompleteness: number;
    duplicatePercentage: number;
    lowQualitySourceCount: number;
    recommendations: string[];
  };
  
  structureQuality: {
    chunkSizeDistribution: {
      small: number; // < 1000 chars
      medium: number; // 1000-4000 chars
      large: number; // > 4000 chars
    };
    orphanedChunks: number;
    overlappingContent: number;
  };
  
  performanceQuality: {
    averageBuildTime: number;
    averageSearchTime: number;
    cacheEfficiency: number;
    memoryEfficiency: number;
  };
}

export interface UsageStatistics {
  projectId: string;
  period: 'day' | 'week' | 'month';
  startDate: Date;
  endDate: Date;
  
  searches: {
    total: number;
    successful: number;
    failed: number;
    averageResponseTime: number;
    popularQueries: Array<{ query: string; count: number }>;
  };
  
  builds: {
    total: number;
    successful: number;
    failed: number;
    averageDuration: number;
    sourcesAdded: number;
    sourcesModified: number;
  };
  
  content: {
    sourcesAdded: number;
    sourcesRemoved: number;
    sourcesModified: number;
    totalContentGrowth: number; // in characters
  };
  
  users: {
    activeUsers: number;
    totalSessions: number;
    averageSessionDuration: number;
  };
}

export class AnalyticsService {
  private static metrics: KnowledgeBaseMetrics[] = [];
  private static events: UserBehaviorEvent[] = [];
  private static performanceMetrics: PerformanceMetric[] = [];

  /**
   * Record knowledge base metrics
   */
  public static recordMetrics(metrics: Omit<KnowledgeBaseMetrics, 'timestamp'>): void {
    this.metrics.push({
      ...metrics,
      timestamp: new Date(),
    });

    // Keep only last 1000 metrics to prevent memory issues
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  /**
   * Track user behavior event
   */
  public static trackEvent(event: Omit<UserBehaviorEvent, 'id' | 'timestamp'>): void {
    this.events.push({
      ...event,
      id: this.generateId(),
      timestamp: new Date(),
    });

    // Keep only last 5000 events
    if (this.events.length > 5000) {
      this.events = this.events.slice(-5000);
    }
  }

  /**
   * Record performance metric
   */
  public static recordPerformance(metric: Omit<PerformanceMetric, 'id' | 'timestamp'>): void {
    this.performanceMetrics.push({
      ...metric,
      id: this.generateId(),
      timestamp: new Date(),
    });

    // Keep only last 2000 performance metrics
    if (this.performanceMetrics.length > 2000) {
      this.performanceMetrics = this.performanceMetrics.slice(-2000);
    }
  }

  /**
   * Generate quality report for a project
   */
  public static generateQualityReport(projectId: string): QualityReport {
    const projectMetrics = this.metrics.filter(m => m.projectId === projectId);
    const projectPerformance = this.performanceMetrics.filter(m => m.projectId === projectId);

    if (projectMetrics.length === 0) {
      return this.getDefaultQualityReport(projectId);
    }

    const latestMetrics = projectMetrics[projectMetrics.length - 1];
    const avgQuality = projectMetrics.reduce((sum, m) => sum + m.averageQualityScore, 0) / projectMetrics.length;
    
    // Calculate performance averages
    const buildTimes = projectPerformance.filter(p => p.metricType === 'build_time');
    const searchTimes = projectPerformance.filter(p => p.metricType === 'search_time');
    const memoryUsage = projectPerformance.filter(p => p.metricType === 'memory_usage');
    
    const avgBuildTime = buildTimes.length > 0 
      ? buildTimes.reduce((sum, p) => sum + p.value, 0) / buildTimes.length 
      : 0;
    
    const avgSearchTime = searchTimes.length > 0
      ? searchTimes.reduce((sum, p) => sum + p.value, 0) / searchTimes.length
      : 0;

    // Calculate chunk size distribution (mock data for now)
    const totalChunks = latestMetrics.totalChunks;
    const chunkSizeDistribution = {
      small: Math.floor(totalChunks * 0.3),
      medium: Math.floor(totalChunks * 0.6),
      large: Math.floor(totalChunks * 0.1),
    };

    const overallScore = Math.round((avgQuality + 
      Math.min(100, 100 - (avgBuildTime / 1000)) + 
      Math.min(100, 100 - (avgSearchTime / 100)) +
      Math.max(0, 100 - latestMetrics.duplicateContent)) / 4);

    return {
      projectId,
      generatedAt: new Date(),
      overallScore,
      
      contentQuality: {
        averageReadability: avgQuality,
        averageCompleteness: Math.min(100, (latestMetrics.totalWords / latestMetrics.totalSources) / 100 * 100),
        duplicatePercentage: latestMetrics.duplicateContent,
        lowQualitySourceCount: latestMetrics.lowQualitySources,
        recommendations: this.generateContentRecommendations(latestMetrics),
      },
      
      structureQuality: {
        chunkSizeDistribution,
        orphanedChunks: latestMetrics.orphanedChunks,
        overlappingContent: Math.floor(latestMetrics.duplicateContent * latestMetrics.totalChunks / 100),
      },
      
      performanceQuality: {
        averageBuildTime: avgBuildTime,
        averageSearchTime: avgSearchTime,
        cacheEfficiency: latestMetrics.cacheHitRate,
        memoryEfficiency: Math.max(0, 100 - (latestMetrics.memoryUsage / (100 * 1024 * 1024)) * 100),
      },
    };
  }

  /**
   * Get usage statistics for a project
   */
  public static getUsageStatistics(
    projectId: string,
    period: 'day' | 'week' | 'month' = 'week'
  ): UsageStatistics {
    const now = new Date();
    const startDate = new Date(now);
    
    switch (period) {
      case 'day':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
    }

    const periodEvents = this.events.filter(e => 
      e.projectId === projectId && 
      e.timestamp >= startDate && 
      e.timestamp <= now
    );

    const searchEvents = periodEvents.filter(e => e.eventType === 'search');
    const buildEvents = periodEvents.filter(e => e.eventType === 'build');
    const sourceEvents = periodEvents.filter(e => 
      ['source_add', 'source_edit', 'source_remove'].includes(e.eventType)
    );

    // Calculate popular queries
    const queryCount = new Map<string, number>();
    searchEvents.forEach(e => {
      if (e.eventData.query) {
        queryCount.set(e.eventData.query, (queryCount.get(e.eventData.query) || 0) + 1);
      }
    });
    
    const popularQueries = Array.from(queryCount.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([query, count]) => ({ query, count }));

    // Calculate unique sessions
    const uniqueSessions = new Set(periodEvents.map(e => e.sessionId)).size;

    return {
      projectId,
      period,
      startDate,
      endDate: now,
      
      searches: {
        total: searchEvents.length,
        successful: searchEvents.filter(e => e.eventData.success).length,
        failed: searchEvents.filter(e => !e.eventData.success).length,
        averageResponseTime: searchEvents.length > 0 
          ? searchEvents.reduce((sum, e) => sum + (e.eventData.duration || 0), 0) / searchEvents.length
          : 0,
        popularQueries,
      },
      
      builds: {
        total: buildEvents.length,
        successful: buildEvents.filter(e => e.eventData.success).length,
        failed: buildEvents.filter(e => !e.eventData.success).length,
        averageDuration: buildEvents.length > 0
          ? buildEvents.reduce((sum, e) => sum + (e.eventData.duration || 0), 0) / buildEvents.length
          : 0,
        sourcesAdded: sourceEvents.filter(e => e.eventType === 'source_add').length,
        sourcesModified: sourceEvents.filter(e => e.eventType === 'source_edit').length,
      },
      
      content: {
        sourcesAdded: sourceEvents.filter(e => e.eventType === 'source_add').length,
        sourcesRemoved: sourceEvents.filter(e => e.eventType === 'source_remove').length,
        sourcesModified: sourceEvents.filter(e => e.eventType === 'source_edit').length,
        totalContentGrowth: 0, // Would need to track content size changes
      },
      
      users: {
        activeUsers: new Set(periodEvents.map(e => e.userId).filter(Boolean)).size,
        totalSessions: uniqueSessions,
        averageSessionDuration: 0, // Would need session tracking
      },
    };
  }

  /**
   * Get performance trends
   */
  public static getPerformanceTrends(
    projectId: string,
    metricType: PerformanceMetric['metricType'],
    days: number = 7
  ): Array<{ date: Date; value: number }> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const relevantMetrics = this.performanceMetrics.filter(m =>
      m.projectId === projectId &&
      m.metricType === metricType &&
      m.timestamp >= cutoffDate
    );

    // Group by day and average
    const dailyAverages = new Map<string, { sum: number; count: number }>();
    
    relevantMetrics.forEach(metric => {
      const dateKey = metric.timestamp.toISOString().split('T')[0];
      const existing = dailyAverages.get(dateKey) || { sum: 0, count: 0 };
      dailyAverages.set(dateKey, {
        sum: existing.sum + metric.value,
        count: existing.count + 1,
      });
    });

    return Array.from(dailyAverages.entries())
      .map(([dateStr, { sum, count }]) => ({
        date: new Date(dateStr),
        value: sum / count,
      }))
      .sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  /**
   * Generate content recommendations
   */
  private static generateContentRecommendations(metrics: KnowledgeBaseMetrics): string[] {
    const recommendations: string[] = [];

    if (metrics.averageQualityScore < 70) {
      recommendations.push('Consider improving content quality - average score is below 70%');
    }

    if (metrics.duplicateContent > 20) {
      recommendations.push('High duplicate content detected - consider deduplication');
    }

    if (metrics.lowQualitySources > metrics.totalSources * 0.3) {
      recommendations.push('Many low-quality sources detected - review and improve content');
    }

    if (metrics.orphanedChunks > 0) {
      recommendations.push('Orphaned chunks found - clean up unused content');
    }

    if (metrics.contentFreshness > 30) {
      recommendations.push('Content is getting stale - consider updating sources');
    }

    if (metrics.buildTime > 30000) {
      recommendations.push('Build time is slow - consider optimizing content processing');
    }

    if (metrics.cacheHitRate < 50) {
      recommendations.push('Low cache hit rate - review caching strategy');
    }

    return recommendations;
  }

  /**
   * Get default quality report
   */
  private static getDefaultQualityReport(projectId: string): QualityReport {
    return {
      projectId,
      generatedAt: new Date(),
      overallScore: 0,
      contentQuality: {
        averageReadability: 0,
        averageCompleteness: 0,
        duplicatePercentage: 0,
        lowQualitySourceCount: 0,
        recommendations: ['No data available - build knowledge base to generate metrics'],
      },
      structureQuality: {
        chunkSizeDistribution: { small: 0, medium: 0, large: 0 },
        orphanedChunks: 0,
        overlappingContent: 0,
      },
      performanceQuality: {
        averageBuildTime: 0,
        averageSearchTime: 0,
        cacheEfficiency: 0,
        memoryEfficiency: 0,
      },
    };
  }

  /**
   * Generate unique ID
   */
  private static generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clear all analytics data (for testing)
   */
  public static clearAllData(): void {
    this.metrics = [];
    this.events = [];
    this.performanceMetrics = [];
  }

  /**
   * Get real-time monitoring data
   */
  public static getMonitoringData(projectId: string): {
    currentMetrics: KnowledgeBaseMetrics | null;
    recentEvents: UserBehaviorEvent[];
    alerts: Array<{
      type: 'warning' | 'error' | 'info';
      message: string;
      timestamp: Date;
    }>;
  } {
    const projectMetrics = this.metrics.filter(m => m.projectId === projectId);
    const currentMetrics = projectMetrics.length > 0 ? projectMetrics[projectMetrics.length - 1] : null;

    const recentEvents = this.events
      .filter(e => e.projectId === projectId)
      .slice(-10)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    const alerts: Array<{
      type: 'warning' | 'error' | 'info';
      message: string;
      timestamp: Date;
    }> = [];

    if (currentMetrics) {
      if (currentMetrics.buildTime > 60000) {
        alerts.push({
          type: 'warning',
          message: 'Build time is unusually high (>60s)',
          timestamp: new Date(),
        });
      }

      if (currentMetrics.cacheHitRate < 30) {
        alerts.push({
          type: 'warning',
          message: 'Cache hit rate is low (<30%)',
          timestamp: new Date(),
        });
      }

      if (currentMetrics.averageQualityScore < 50) {
        alerts.push({
          type: 'error',
          message: 'Content quality is poor (<50%)',
          timestamp: new Date(),
        });
      }

      if (currentMetrics.failedBuilds > currentMetrics.successfulBuilds) {
        alerts.push({
          type: 'error',
          message: 'More builds are failing than succeeding',
          timestamp: new Date(),
        });
      }
    }

    return {
      currentMetrics,
      recentEvents,
      alerts,
    };
  }

  /**
   * Export analytics data
   */
  public static exportData(projectId?: string): {
    metrics: KnowledgeBaseMetrics[];
    events: UserBehaviorEvent[];
    performanceMetrics: PerformanceMetric[];
  } {
    const filter = projectId ? (item: any) => item.projectId === projectId : () => true;

    return {
      metrics: this.metrics.filter(filter),
      events: this.events.filter(filter),
      performanceMetrics: this.performanceMetrics.filter(filter),
    };
  }
}
