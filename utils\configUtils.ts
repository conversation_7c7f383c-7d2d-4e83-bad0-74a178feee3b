import { ProjectConfig } from '../types';

// Default project configuration
export const DEFAULT_PROJECT_CONFIG: ProjectConfig = {
  themeColor: '#4f46e5',
  welcomeMessage: "Hello! I'm here to help you with any questions you might have. How can I assist you today?",
  suggestedQuestions: [
    "What are your business hours?",
    "How can I contact support?",
    "What products do you offer?",
    "What is your return policy?"
  ],
  systemPrompt: 'You are a helpful AI assistant for this website. Use the provided context to answer questions accurately and helpfully. If you cannot find the answer in the context, politely say so and suggest contacting support.',
};

/**
 * Ensures a ProjectConfig object has all required fields with proper defaults
 * This prevents runtime errors when accessing config properties
 */
export const ensureValidProjectConfig = (config: Partial<ProjectConfig> | undefined | null): ProjectConfig => {
  if (!config || typeof config !== 'object') {
    return { ...DEFAULT_PROJECT_CONFIG };
  }

  return {
    themeColor: typeof config.themeColor === 'string' ? config.themeColor : DEFAULT_PROJECT_CONFIG.themeColor,
    welcomeMessage: typeof config.welcomeMessage === 'string' ? config.welcomeMessage : DEFAULT_PROJECT_CONFIG.welcomeMessage,
    suggestedQuestions: Array.isArray(config.suggestedQuestions) ? config.suggestedQuestions : DEFAULT_PROJECT_CONFIG.suggestedQuestions,
    systemPrompt: typeof config.systemPrompt === 'string' ? config.systemPrompt : DEFAULT_PROJECT_CONFIG.systemPrompt,
    headerLogo: typeof config.headerLogo === 'string' ? config.headerLogo : undefined,
  };
};

/**
 * Safely gets suggested questions as an array, with fallback to empty array
 */
export const getSafeArrayFromConfig = <T>(value: T[] | undefined | null, fallback: T[] = []): T[] => {
  return Array.isArray(value) ? value : fallback;
};

/**
 * Validates and sanitizes suggested questions
 */
export const validateSuggestedQuestions = (questions: unknown): string[] => {
  if (!Array.isArray(questions)) {
    return DEFAULT_PROJECT_CONFIG.suggestedQuestions;
  }
  
  return questions
    .filter(q => typeof q === 'string' && q.trim().length > 0)
    .slice(0, 4); // Limit to 4 questions max
};
