/**
 * Enhanced file processor service with support for multiple file formats
 */

export interface FileProcessorConfig {
  maxFileSize: number;
  supportedFormats: string[];
  enableOCR?: boolean;
  preserveFormatting?: boolean;
  extractMetadata?: boolean;
}

export interface ProcessedFileResult {
  content: string;
  metadata: {
    fileName: string;
    fileSize: number;
    mimeType: string;
    format: string;
    pageCount?: number;
    wordCount: number;
    characterCount: number;
    processingTime: number;
    extractionMethod: string;
    warnings?: string[];
  };
}

export interface FileFormatHandler {
  canHandle(file: File): boolean;
  process(file: File, config: FileProcessorConfig): Promise<ProcessedFileResult>;
  getFormatInfo(): {
    name: string;
    extensions: string[];
    mimeTypes: string[];
    description: string;
  };
}

export class FileProcessorService {
  private handlers = new Map<string, FileFormatHandler>();
  private config: FileProcessorConfig;

  constructor(config: Partial<FileProcessorConfig> = {}) {
    this.config = {
      maxFileSize: 10 * 1024 * 1024, // 10MB
      supportedFormats: ['.txt', '.md', '.pdf', '.docx', '.doc', '.rtf', '.html', '.csv'],
      enableOCR: false,
      preserveFormatting: false,
      extractMetadata: true,
      ...config,
    };

    this.registerDefaultHandlers();
  }

  /**
   * Register default file format handlers
   */
  private registerDefaultHandlers(): void {
    this.registerHandler(new TextFileHandler());
    this.registerHandler(new MarkdownFileHandler());
    this.registerHandler(new PDFFileHandler());
    this.registerHandler(new DocxFileHandler());
    this.registerHandler(new HTMLFileHandler());
    this.registerHandler(new CSVFileHandler());
    this.registerHandler(new RTFFileHandler());
  }

  /**
   * Register a file format handler
   */
  public registerHandler(handler: FileFormatHandler): void {
    const info = handler.getFormatInfo();
    this.handlers.set(info.name, handler);
  }

  /**
   * Process a file and extract its content
   */
  public async processFile(file: File): Promise<ProcessedFileResult> {
    // Validate file size
    if (file.size > this.config.maxFileSize) {
      throw new Error(`File size (${(file.size / 1024 / 1024).toFixed(1)}MB) exceeds maximum allowed size (${(this.config.maxFileSize / 1024 / 1024).toFixed(1)}MB)`);
    }

    // Find appropriate handler
    const handler = this.findHandler(file);
    if (!handler) {
      throw new Error(`Unsupported file format: ${file.type || 'unknown'}. Supported formats: ${this.config.supportedFormats.join(', ')}`);
    }

    try {
      const result = await handler.process(file, this.config);
      
      // Validate result
      if (!result.content || result.content.trim().length === 0) {
        throw new Error('No content could be extracted from the file');
      }

      return result;
    } catch (error) {
      throw new Error(`Failed to process file: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Find appropriate handler for a file
   */
  private findHandler(file: File): FileFormatHandler | null {
    for (const handler of this.handlers.values()) {
      if (handler.canHandle(file)) {
        return handler;
      }
    }
    return null;
  }

  /**
   * Get supported file formats
   */
  public getSupportedFormats(): Array<{
    name: string;
    extensions: string[];
    mimeTypes: string[];
    description: string;
  }> {
    return Array.from(this.handlers.values()).map(handler => handler.getFormatInfo());
  }

  /**
   * Check if a file is supported
   */
  public isSupported(file: File): boolean {
    return this.findHandler(file) !== null;
  }
}

// Text File Handler
class TextFileHandler implements FileFormatHandler {
  canHandle(file: File): boolean {
    return file.type === 'text/plain' || file.name.toLowerCase().endsWith('.txt');
  }

  async process(file: File, config: FileProcessorConfig): Promise<ProcessedFileResult> {
    const startTime = Date.now();
    const content = await file.text();
    
    return {
      content,
      metadata: {
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        format: 'text',
        wordCount: content.split(/\s+/).filter(word => word.length > 0).length,
        characterCount: content.length,
        processingTime: Date.now() - startTime,
        extractionMethod: 'native',
      },
    };
  }

  getFormatInfo() {
    return {
      name: 'text',
      extensions: ['.txt'],
      mimeTypes: ['text/plain'],
      description: 'Plain text files',
    };
  }
}

// Markdown File Handler
class MarkdownFileHandler implements FileFormatHandler {
  canHandle(file: File): boolean {
    return file.type === 'text/markdown' || 
           file.name.toLowerCase().endsWith('.md') ||
           file.name.toLowerCase().endsWith('.markdown');
  }

  async process(file: File, config: FileProcessorConfig): Promise<ProcessedFileResult> {
    const startTime = Date.now();
    let content = await file.text();
    
    // Basic markdown processing - remove markdown syntax if not preserving formatting
    if (!config.preserveFormatting) {
      content = content
        .replace(/^#{1,6}\s+/gm, '') // Remove headers
        .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
        .replace(/\*(.*?)\*/g, '$1') // Remove italic
        .replace(/`(.*?)`/g, '$1') // Remove inline code
        .replace(/```[\s\S]*?```/g, '') // Remove code blocks
        .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Convert links to text
        .replace(/^\s*[-*+]\s+/gm, '') // Remove list markers
        .replace(/^\s*\d+\.\s+/gm, ''); // Remove numbered list markers
    }
    
    return {
      content,
      metadata: {
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        format: 'markdown',
        wordCount: content.split(/\s+/).filter(word => word.length > 0).length,
        characterCount: content.length,
        processingTime: Date.now() - startTime,
        extractionMethod: 'markdown-parser',
      },
    };
  }

  getFormatInfo() {
    return {
      name: 'markdown',
      extensions: ['.md', '.markdown'],
      mimeTypes: ['text/markdown', 'text/x-markdown'],
      description: 'Markdown formatted text files',
    };
  }
}

// PDF File Handler (placeholder - would need a PDF library in real implementation)
class PDFFileHandler implements FileFormatHandler {
  canHandle(file: File): boolean {
    return file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf');
  }

  async process(file: File, config: FileProcessorConfig): Promise<ProcessedFileResult> {
    const startTime = Date.now();
    
    // Placeholder implementation - in a real app, you'd use a library like pdf-parse or PDF.js
    throw new Error('PDF processing is not yet supported. Please convert to text format.');
    
    // Example of what the implementation might look like:
    /*
    const pdfBuffer = await file.arrayBuffer();
    const pdfData = await pdfParse(pdfBuffer);
    
    return {
      content: pdfData.text,
      metadata: {
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        format: 'pdf',
        pageCount: pdfData.numpages,
        wordCount: pdfData.text.split(/\s+/).filter(word => word.length > 0).length,
        characterCount: pdfData.text.length,
        processingTime: Date.now() - startTime,
        extractionMethod: 'pdf-parse',
      },
    };
    */
  }

  getFormatInfo() {
    return {
      name: 'pdf',
      extensions: ['.pdf'],
      mimeTypes: ['application/pdf'],
      description: 'Portable Document Format files',
    };
  }
}

// DOCX File Handler (placeholder)
class DocxFileHandler implements FileFormatHandler {
  canHandle(file: File): boolean {
    return file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
           file.name.toLowerCase().endsWith('.docx');
  }

  async process(file: File, config: FileProcessorConfig): Promise<ProcessedFileResult> {
    const startTime = Date.now();
    
    // Placeholder implementation - would use a library like mammoth.js
    throw new Error('DOCX processing is not yet supported. Please convert to text format.');
  }

  getFormatInfo() {
    return {
      name: 'docx',
      extensions: ['.docx'],
      mimeTypes: ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      description: 'Microsoft Word documents',
    };
  }
}

// HTML File Handler
class HTMLFileHandler implements FileFormatHandler {
  canHandle(file: File): boolean {
    return file.type === 'text/html' || file.name.toLowerCase().endsWith('.html') || file.name.toLowerCase().endsWith('.htm');
  }

  async process(file: File, config: FileProcessorConfig): Promise<ProcessedFileResult> {
    const startTime = Date.now();
    let content = await file.text();
    
    // Basic HTML tag removal
    if (!config.preserveFormatting) {
      content = content
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
        .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // Remove styles
        .replace(/<[^>]+>/g, ' ') // Remove HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&') // Replace HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();
    }
    
    return {
      content,
      metadata: {
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        format: 'html',
        wordCount: content.split(/\s+/).filter(word => word.length > 0).length,
        characterCount: content.length,
        processingTime: Date.now() - startTime,
        extractionMethod: 'html-parser',
      },
    };
  }

  getFormatInfo() {
    return {
      name: 'html',
      extensions: ['.html', '.htm'],
      mimeTypes: ['text/html'],
      description: 'HTML web pages',
    };
  }
}

// CSV File Handler
class CSVFileHandler implements FileFormatHandler {
  canHandle(file: File): boolean {
    return file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv');
  }

  async process(file: File, config: FileProcessorConfig): Promise<ProcessedFileResult> {
    const startTime = Date.now();
    const content = await file.text();
    
    // Convert CSV to readable text format
    const lines = content.split('\n');
    const processedContent = lines
      .map(line => line.split(',').join(' | '))
      .join('\n');
    
    return {
      content: processedContent,
      metadata: {
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        format: 'csv',
        wordCount: processedContent.split(/\s+/).filter(word => word.length > 0).length,
        characterCount: processedContent.length,
        processingTime: Date.now() - startTime,
        extractionMethod: 'csv-parser',
      },
    };
  }

  getFormatInfo() {
    return {
      name: 'csv',
      extensions: ['.csv'],
      mimeTypes: ['text/csv'],
      description: 'Comma-separated values files',
    };
  }
}

// RTF File Handler (placeholder)
class RTFFileHandler implements FileFormatHandler {
  canHandle(file: File): boolean {
    return file.type === 'application/rtf' || file.name.toLowerCase().endsWith('.rtf');
  }

  async process(file: File, config: FileProcessorConfig): Promise<ProcessedFileResult> {
    const startTime = Date.now();
    
    // Placeholder implementation - would need an RTF parser
    throw new Error('RTF processing is not yet supported. Please convert to text format.');
  }

  getFormatInfo() {
    return {
      name: 'rtf',
      extensions: ['.rtf'],
      mimeTypes: ['application/rtf', 'text/rtf'],
      description: 'Rich Text Format files',
    };
  }
}

// Global file processor instance
export const fileProcessor = new FileProcessorService({
  maxFileSize: 10 * 1024 * 1024, // 10MB
  supportedFormats: ['.txt', '.md', '.html', '.csv'],
  preserveFormatting: false,
  extractMetadata: true,
});
