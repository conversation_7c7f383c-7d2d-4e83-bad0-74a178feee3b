/**
 * Mock services for testing knowledge base functionality
 */

import { vi } from 'vitest';
import { DataSource, SourceType, KnowledgeBaseStats } from '../../types/knowledgeBase';

// Mock project service
export const mockProjectService = {
  processAndStoreContent: vi.fn().mockImplementation(async (
    projectId: string,
    content: string,
    sourceType: string,
    reference: string
  ) => {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
    
    // Simulate success/failure based on content
    if (content.includes('FAIL')) {
      throw new Error('Simulated processing failure');
    }
    
    return {
      chunkIds: [`chunk-${Date.now()}-1`, `chunk-${Date.now()}-2`],
      processingTime: Math.random() * 1000,
    };
  }),

  getProjectKnowledgeBaseStats: vi.fn().mockImplementation(async (projectId: string): Promise<KnowledgeBaseStats> => {
    // Simulate different stats based on project ID
    const baseStats = {
      chunkCount: 10,
      sourceCount: 3,
      totalWords: 1500,
      lastUpdated: new Date(),
    };

    if (projectId.includes('large')) {
      return {
        ...baseStats,
        chunkCount: 100,
        sourceCount: 20,
        totalWords: 15000,
      };
    }

    if (projectId.includes('empty')) {
      return {
        ...baseStats,
        chunkCount: 0,
        sourceCount: 0,
        totalWords: 0,
      };
    }

    return baseStats;
  }),

  clearProjectKnowledgeBase: vi.fn().mockImplementation(async (projectId: string) => {
    await new Promise(resolve => setTimeout(resolve, 50));
    return { clearedChunks: Math.floor(Math.random() * 50) };
  }),

  processFile: vi.fn().mockImplementation(async (file: File) => {
    const content = await file.text();
    return {
      content,
      metadata: {
        fileName: file.name,
        fileSize: file.size,
        processingTime: Math.random() * 500,
      },
    };
  }),
};

// Mock Firestore service
export const mockFirestoreService = {
  collection: vi.fn().mockReturnValue({
    doc: vi.fn().mockReturnValue({
      set: vi.fn().mockResolvedValue(undefined),
      get: vi.fn().mockResolvedValue({
        exists: true,
        data: () => ({
          id: 'mock-doc',
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
      }),
      update: vi.fn().mockResolvedValue(undefined),
      delete: vi.fn().mockResolvedValue(undefined),
    }),
    add: vi.fn().mockResolvedValue({ id: 'mock-doc-id' }),
    where: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    get: vi.fn().mockResolvedValue({
      docs: [
        {
          id: 'mock-doc-1',
          data: () => ({ name: 'Mock Document 1' }),
        },
        {
          id: 'mock-doc-2',
          data: () => ({ name: 'Mock Document 2' }),
        },
      ],
    }),
  }),
};

// Mock Gemini service
export const mockGeminiService = {
  generateEmbedding: vi.fn().mockImplementation(async (text: string) => {
    // Simulate embedding generation delay
    await new Promise(resolve => setTimeout(resolve, Math.random() * 200));
    
    // Generate mock embedding (768 dimensions)
    const embedding = Array.from({ length: 768 }, () => Math.random() - 0.5);
    
    return {
      embedding,
      tokenCount: text.split(' ').length,
      processingTime: Math.random() * 100,
    };
  }),

  generateResponse: vi.fn().mockImplementation(async (prompt: string, context?: string) => {
    await new Promise(resolve => setTimeout(resolve, Math.random() * 500));
    
    // Generate mock response based on prompt
    if (prompt.toLowerCase().includes('error')) {
      throw new Error('Simulated Gemini API error');
    }
    
    return {
      response: `Mock response for: ${prompt.substring(0, 50)}...`,
      tokenCount: Math.floor(Math.random() * 100) + 50,
      processingTime: Math.random() * 300,
    };
  }),

  batchGenerateEmbeddings: vi.fn().mockImplementation(async (texts: string[]) => {
    await new Promise(resolve => setTimeout(resolve, texts.length * 50));
    
    return texts.map((text, index) => ({
      embedding: Array.from({ length: 768 }, () => Math.random() - 0.5),
      tokenCount: text.split(' ').length,
      index,
    }));
  }),
};

// Mock fetch for URL processing
export const mockFetch = vi.fn().mockImplementation(async (url: string) => {
  await new Promise(resolve => setTimeout(resolve, Math.random() * 300));
  
  if (url.includes('invalid') || url.includes('404')) {
    return {
      ok: false,
      status: 404,
      statusText: 'Not Found',
    };
  }
  
  if (url.includes('timeout')) {
    await new Promise(resolve => setTimeout(resolve, 10000));
  }
  
  if (url.includes('large')) {
    const largeContent = 'Large content from URL. '.repeat(10000);
    return {
      ok: true,
      status: 200,
      text: async () => largeContent,
      json: async () => ({ content: largeContent }),
    };
  }
  
  const mockContent = `Mock content from ${url}. This is sample content that would be fetched from a real URL.`;
  
  return {
    ok: true,
    status: 200,
    text: async () => mockContent,
    json: async () => ({ content: mockContent }),
  };
});

// Mock file system operations
export const mockFileSystem = {
  readFile: vi.fn().mockImplementation(async (path: string) => {
    if (path.includes('nonexistent')) {
      throw new Error('File not found');
    }
    
    return `Mock file content from ${path}`;
  }),

  writeFile: vi.fn().mockImplementation(async (path: string, content: string) => {
    if (path.includes('readonly')) {
      throw new Error('Permission denied');
    }
    
    return { bytesWritten: content.length };
  }),

  exists: vi.fn().mockImplementation(async (path: string) => {
    return !path.includes('nonexistent');
  }),
};

// Mock analytics data
export const mockAnalyticsData = {
  metrics: [
    {
      projectId: 'test-project',
      timestamp: new Date(),
      totalSources: 5,
      totalChunks: 50,
      totalWords: 5000,
      totalCharacters: 25000,
      averageQualityScore: 85,
      buildTime: 2500,
      searchResponseTime: 150,
      memoryUsage: 10 * 1024 * 1024,
      cacheHitRate: 75,
      searchQueries: 20,
      successfulBuilds: 8,
      failedBuilds: 2,
      userSessions: 5,
      duplicateContent: 5,
      lowQualitySources: 1,
      orphanedChunks: 0,
      contentFreshness: 2,
    },
  ],
  
  events: [
    {
      id: 'event-1',
      projectId: 'test-project',
      sessionId: 'session-1',
      timestamp: new Date(),
      eventType: 'search' as const,
      eventData: {
        query: 'test query',
        success: true,
        duration: 150,
      },
    },
    {
      id: 'event-2',
      projectId: 'test-project',
      sessionId: 'session-1',
      timestamp: new Date(),
      eventType: 'build' as const,
      eventData: {
        success: true,
        duration: 2500,
      },
    },
  ],
};

// Mock test data generators
export const mockDataGenerators = {
  createMockSource: (overrides: Partial<DataSource> = {}): DataSource => ({
    id: `mock-source-${Date.now()}`,
    type: SourceType.TEXT,
    reference: 'Mock Document',
    content: 'This is mock content for testing purposes. It contains multiple sentences to test chunking.',
    addedAt: new Date(),
    ...overrides,
  }),

  createMockSources: (count: number, contentSize: 'small' | 'medium' | 'large' = 'medium'): DataSource[] => {
    const contentSizes = {
      small: 'Short content. ',
      medium: 'Medium length content for testing. '.repeat(10),
      large: 'Large content for performance testing. '.repeat(100),
    };
    
    return Array.from({ length: count }, (_, i) => ({
      id: `mock-source-${i}`,
      type: SourceType.TEXT,
      reference: `Mock Document ${i}`,
      content: contentSizes[contentSize] + `Unique content ${i}.`,
      addedAt: new Date(),
    }));
  },

  createMockFile: (name: string, content: string, type: string = 'text/plain'): File => {
    return new File([content], name, { type });
  },

  createMockFiles: (count: number, sizeKB: number = 1): File[] => {
    const content = 'Mock file content. '.repeat(Math.floor(sizeKB * 1024 / 20));
    
    return Array.from({ length: count }, (_, i) => 
      new File([content], `mock-file-${i}.txt`, { type: 'text/plain' })
    );
  },
};

// Mock error scenarios
export const mockErrorScenarios = {
  networkError: () => {
    throw new Error('Network request failed');
  },

  timeoutError: () => {
    throw new Error('Request timed out');
  },

  validationError: () => {
    throw new Error('Invalid input provided');
  },

  permissionError: () => {
    throw new Error('Permission denied');
  },

  processingError: () => {
    throw new Error('Failed to process content');
  },

  quotaExceededError: () => {
    throw new Error('Quota exceeded');
  },
};

// Mock performance scenarios
export const mockPerformanceScenarios = {
  slowOperation: async (delayMs: number = 1000) => {
    await new Promise(resolve => setTimeout(resolve, delayMs));
    return 'Slow operation completed';
  },

  memoryIntensiveOperation: () => {
    // Simulate memory-intensive operation
    const largeArray = new Array(1000000).fill('memory test');
    return largeArray.length;
  },

  cpuIntensiveOperation: () => {
    // Simulate CPU-intensive operation
    let result = 0;
    for (let i = 0; i < 1000000; i++) {
      result += Math.sqrt(i);
    }
    return result;
  },
};

// Setup and teardown helpers
export const mockHelpers = {
  setupMocks: () => {
    // Setup global mocks
    global.fetch = mockFetch;
    
    // Mock console methods to reduce test noise
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  },

  teardownMocks: () => {
    // Restore all mocks
    vi.restoreAllMocks();
  },

  resetMockCalls: () => {
    // Reset call counts for all mocks
    Object.values(mockProjectService).forEach(mock => {
      if (vi.isMockFunction(mock)) {
        mock.mockClear();
      }
    });
    
    Object.values(mockGeminiService).forEach(mock => {
      if (vi.isMockFunction(mock)) {
        mock.mockClear();
      }
    });
    
    mockFetch.mockClear();
  },

  expectMockCalls: (mockFn: any, expectedCalls: number) => {
    expect(vi.isMockFunction(mockFn)).toBe(true);
    expect(mockFn).toHaveBeenCalledTimes(expectedCalls);
  },
};

// Export all mocks
export {
  mockProjectService,
  mockFirestoreService,
  mockGeminiService,
  mockFetch,
  mockFileSystem,
  mockAnalyticsData,
  mockDataGenerators,
  mockErrorScenarios,
  mockPerformanceScenarios,
  mockHelpers,
};
