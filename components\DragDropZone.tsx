import React, { useState, useRef, useCallback } from 'react';
import { useToast } from './Toast';
import { EnhancedErrorHandler } from '../utils/errorHandling';
import { fileProcessor } from '../services/fileProcessorService';

export interface FileWithPreview {
  file: File;
  id: string;
  preview?: string;
  error?: string;
  processing?: boolean;
}

export interface DragDropZoneProps {
  onFilesAdded: (files: FileWithPreview[]) => void;
  onFileRemoved: (fileId: string) => void;
  acceptedTypes?: string[];
  maxFileSize?: number; // in bytes
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
  showPreview?: boolean;
  multiple?: boolean;
}

// Get supported formats from file processor
const getSupportedFormats = () => {
  const formats = fileProcessor.getSupportedFormats();
  const extensions = formats.flatMap(format => format.extensions);
  const mimeTypes = formats.flatMap(format => format.mimeTypes);
  return [...extensions, ...mimeTypes];
};

const DEFAULT_ACCEPTED_TYPES = getSupportedFormats();
const DEFAULT_MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const DEFAULT_MAX_FILES = 10;

export const DragDropZone: React.FC<DragDropZoneProps> = ({
  onFilesAdded,
  onFileRemoved,
  acceptedTypes = DEFAULT_ACCEPTED_TYPES,
  maxFileSize = DEFAULT_MAX_FILE_SIZE,
  maxFiles = DEFAULT_MAX_FILES,
  disabled = false,
  className = '',
  showPreview = true,
  multiple = true,
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { addToast } = useToast();

  const validateFile = useCallback((file: File): string | null => {
    // Check file size
    if (file.size > maxFileSize) {
      return `File size (${(file.size / 1024 / 1024).toFixed(1)}MB) exceeds maximum allowed size (${(maxFileSize / 1024 / 1024).toFixed(1)}MB)`;
    }

    // Use file processor to check if file is supported
    if (!fileProcessor.isSupported(file)) {
      const supportedFormats = fileProcessor.getSupportedFormats()
        .map(format => `${format.name} (${format.extensions.join(', ')})`)
        .join(', ');
      return `File type not supported. Supported formats: ${supportedFormats}`;
    }

    return null;
  }, [maxFileSize]);

  const processFiles = useCallback(async (fileList: FileList | File[]) => {
    const newFiles: FileWithPreview[] = [];
    const errors: string[] = [];

    // Convert FileList to Array
    const filesArray = Array.from(fileList);

    // Check total file count
    if (files.length + filesArray.length > maxFiles) {
      addToast({
        type: 'warning',
        title: 'Too Many Files',
        message: `Maximum ${maxFiles} files allowed. Some files were not added.`,
      });
      filesArray.splice(maxFiles - files.length);
    }

    for (const file of filesArray) {
      const validationError = validateFile(file);
      const fileWithPreview: FileWithPreview = {
        file,
        id: `${file.name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        error: validationError || undefined,
        processing: false,
      };

      // Generate preview for text files
      if (showPreview && !validationError && file.type.startsWith('text/')) {
        try {
          const text = await file.text();
          fileWithPreview.preview = text.substring(0, 200) + (text.length > 200 ? '...' : '');
        } catch (error) {
          fileWithPreview.error = 'Failed to read file content';
        }
      }

      newFiles.push(fileWithPreview);

      if (validationError) {
        errors.push(`${file.name}: ${validationError}`);
      }
    }

    if (errors.length > 0) {
      addToast({
        type: 'error',
        title: 'File Validation Errors',
        message: `${errors.length} file(s) had errors. Check the file list for details.`,
        duration: 6000,
      });
    }

    const validFiles = newFiles.filter(f => !f.error);
    if (validFiles.length > 0) {
      addToast({
        type: 'success',
        title: 'Files Added',
        message: `${validFiles.length} file(s) added successfully`,
      });
    }

    setFiles(prev => [...prev, ...newFiles]);
    onFilesAdded(newFiles);
  }, [files.length, maxFiles, validateFile, showPreview, addToast, onFilesAdded]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    if (disabled) return;

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      processFiles(droppedFiles);
    }
  }, [disabled, processFiles]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      processFiles(selectedFiles);
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [processFiles]);

  const handleRemoveFile = useCallback((fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
    onFileRemoved(fileId);
    
    addToast({
      type: 'info',
      title: 'File Removed',
      message: 'File has been removed from the list',
    });
  }, [onFileRemoved, addToast]);

  const handleBrowseClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled]);

  const getDropZoneClasses = () => {
    const baseClasses = "border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200";
    
    if (disabled) {
      return `${baseClasses} border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 cursor-not-allowed opacity-50`;
    }
    
    if (isDragOver) {
      return `${baseClasses} border-primary bg-primary/10 dark:bg-primary/20`;
    }
    
    return `${baseClasses} border-gray-300 dark:border-gray-600 hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer`;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Drop Zone */}
      <div
        className={getDropZoneClasses()}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleBrowseClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled}
        />
        
        <div className="flex flex-col items-center gap-4">
          <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {isDragOver ? 'Drop files here' : 'Drag & drop files here'}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              or <span className="text-primary font-medium">browse</span> to choose files
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-500">
              Supported: {acceptedTypes.join(', ')} • Max {formatFileSize(maxFileSize)} • Up to {maxFiles} files
            </p>
          </div>
        </div>
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-medium text-gray-900 dark:text-gray-100">
            Selected Files ({files.length})
          </h4>
          
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {files.map((fileWithPreview) => (
              <FilePreviewCard
                key={fileWithPreview.id}
                fileWithPreview={fileWithPreview}
                onRemove={handleRemoveFile}
                showPreview={showPreview}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

interface FilePreviewCardProps {
  fileWithPreview: FileWithPreview;
  onRemove: (fileId: string) => void;
  showPreview: boolean;
}

const FilePreviewCard: React.FC<FilePreviewCardProps> = ({
  fileWithPreview,
  onRemove,
  showPreview,
}) => {
  const { file, id, preview, error, processing } = fileWithPreview;

  const getFileIcon = () => {
    if (file.type.includes('text')) {
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      );
    }
    return (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
    );
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`p-3 border rounded-lg ${error ? 'border-red-300 bg-red-50 dark:border-red-700 dark:bg-red-900/20' : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'}`}>
      <div className="flex items-start gap-3">
        <div className={`flex-shrink-0 ${error ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'}`}>
          {getFileIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h5 className={`text-sm font-medium truncate ${error ? 'text-red-800 dark:text-red-200' : 'text-gray-900 dark:text-gray-100'}`}>
              {file.name}
            </h5>
            <button
              onClick={() => onRemove(id)}
              className="flex-shrink-0 text-gray-400 hover:text-red-500 transition-colors"
              disabled={processing}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <p className={`text-xs ${error ? 'text-red-600 dark:text-red-400' : 'text-gray-500 dark:text-gray-400'}`}>
            {formatFileSize(file.size)} • {file.type || 'Unknown type'}
          </p>
          
          {error && (
            <p className="text-xs text-red-600 dark:text-red-400 mt-1">
              ⚠️ {error}
            </p>
          )}
          
          {showPreview && preview && !error && (
            <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs text-gray-600 dark:text-gray-300">
              <strong>Preview:</strong> {preview}
            </div>
          )}
          
          {processing && (
            <div className="mt-2 flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400">
              <svg className="w-3 h-3 animate-spin" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              Processing...
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
