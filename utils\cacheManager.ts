/**
 * Cache manager for knowledge base operations with memory optimization
 */

export interface CacheEntry<T = any> {
  key: string;
  value: T;
  timestamp: Date;
  expiresAt?: Date;
  size: number;
  accessCount: number;
  lastAccessed: Date;
  metadata?: Record<string, any>;
}

export interface CacheConfig {
  maxSize: number; // Maximum cache size in bytes
  maxEntries: number; // Maximum number of entries
  defaultTTL: number; // Default time to live in milliseconds
  cleanupInterval: number; // Cleanup interval in milliseconds
  enableCompression?: boolean;
  enableMetrics?: boolean;
}

export interface CacheMetrics {
  totalEntries: number;
  totalSize: number;
  hitCount: number;
  missCount: number;
  evictionCount: number;
  hitRate: number;
  memoryUsage: {
    used: number;
    available: number;
    percentage: number;
  };
}

export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  compress?: boolean;
  priority?: 'low' | 'normal' | 'high';
  tags?: string[]; // For cache invalidation by tags
}

export class CacheManager {
  private cache = new Map<string, CacheEntry>();
  private config: Required<CacheConfig>;
  private metrics: CacheMetrics;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 100 * 1024 * 1024, // 100MB default
      maxEntries: 1000,
      defaultTTL: 30 * 60 * 1000, // 30 minutes
      cleanupInterval: 5 * 60 * 1000, // 5 minutes
      enableCompression: false,
      enableMetrics: true,
      ...config,
    };

    this.metrics = {
      totalEntries: 0,
      totalSize: 0,
      hitCount: 0,
      missCount: 0,
      evictionCount: 0,
      hitRate: 0,
      memoryUsage: { used: 0, available: this.config.maxSize, percentage: 0 },
    };

    this.startCleanupTimer();
  }

  /**
   * Get value from cache
   */
  public get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.updateMetrics('miss');
      return null;
    }

    // Check if entry has expired
    if (entry.expiresAt && entry.expiresAt < new Date()) {
      this.cache.delete(key);
      this.updateMetrics('miss');
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = new Date();
    
    this.updateMetrics('hit');
    return entry.value as T;
  }

  /**
   * Set value in cache
   */
  public set<T>(key: string, value: T, options: CacheOptions = {}): boolean {
    const size = this.calculateSize(value);
    const ttl = options.ttl ?? this.config.defaultTTL;
    const expiresAt = ttl > 0 ? new Date(Date.now() + ttl) : undefined;

    // Check if we need to make space
    if (!this.hasSpace(size)) {
      if (!this.makeSpace(size)) {
        return false; // Could not make enough space
      }
    }

    const entry: CacheEntry<T> = {
      key,
      value: options.compress && this.config.enableCompression ? this.compress(value) : value,
      timestamp: new Date(),
      expiresAt,
      size,
      accessCount: 0,
      lastAccessed: new Date(),
      metadata: {
        priority: options.priority ?? 'normal',
        tags: options.tags ?? [],
        compressed: options.compress && this.config.enableCompression,
      },
    };

    this.cache.set(key, entry);
    this.updateCacheSize();
    return true;
  }

  /**
   * Delete value from cache
   */
  public delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.updateCacheSize();
    }
    return deleted;
  }

  /**
   * Clear cache by tags
   */
  public clearByTags(tags: string[]): number {
    let cleared = 0;
    const tagSet = new Set(tags);

    for (const [key, entry] of this.cache.entries()) {
      const entryTags = entry.metadata?.tags || [];
      if (entryTags.some((tag: string) => tagSet.has(tag))) {
        this.cache.delete(key);
        cleared++;
      }
    }

    if (cleared > 0) {
      this.updateCacheSize();
    }

    return cleared;
  }

  /**
   * Clear all cache entries
   */
  public clear(): void {
    this.cache.clear();
    this.updateCacheSize();
  }

  /**
   * Get cache metrics
   */
  public getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  /**
   * Get cache keys
   */
  public keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Check if cache has key
   */
  public has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    // Check expiration
    if (entry.expiresAt && entry.expiresAt < new Date()) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Get cache size information
   */
  public getSizeInfo(): {
    entries: number;
    totalSize: number;
    averageSize: number;
    maxSize: number;
    utilizationPercentage: number;
  } {
    const totalSize = this.metrics.totalSize;
    const entries = this.cache.size;
    
    return {
      entries,
      totalSize,
      averageSize: entries > 0 ? totalSize / entries : 0,
      maxSize: this.config.maxSize,
      utilizationPercentage: (totalSize / this.config.maxSize) * 100,
    };
  }

  /**
   * Optimize cache by removing least recently used entries
   */
  public optimize(): {
    entriesRemoved: number;
    sizeFreed: number;
  } {
    const entries = Array.from(this.cache.entries());
    
    // Sort by last accessed time (oldest first)
    entries.sort(([, a], [, b]) => a.lastAccessed.getTime() - b.lastAccessed.getTime());
    
    let entriesRemoved = 0;
    let sizeFreed = 0;
    const targetSize = this.config.maxSize * 0.8; // Target 80% utilization
    
    while (this.metrics.totalSize > targetSize && entries.length > entriesRemoved) {
      const [key, entry] = entries[entriesRemoved];
      this.cache.delete(key);
      sizeFreed += entry.size;
      entriesRemoved++;
      this.metrics.evictionCount++;
    }
    
    this.updateCacheSize();
    
    return { entriesRemoved, sizeFreed };
  }

  /**
   * Calculate size of a value
   */
  private calculateSize(value: any): number {
    if (value === null || value === undefined) return 0;
    
    if (typeof value === 'string') {
      return value.length * 2; // Approximate UTF-16 encoding
    }
    
    if (typeof value === 'number') {
      return 8; // 64-bit number
    }
    
    if (typeof value === 'boolean') {
      return 1;
    }
    
    if (value instanceof ArrayBuffer) {
      return value.byteLength;
    }
    
    // For objects, use JSON string length as approximation
    try {
      return JSON.stringify(value).length * 2;
    } catch {
      return 1000; // Default size for non-serializable objects
    }
  }

  /**
   * Check if cache has space for new entry
   */
  private hasSpace(size: number): boolean {
    return (
      this.cache.size < this.config.maxEntries &&
      this.metrics.totalSize + size <= this.config.maxSize
    );
  }

  /**
   * Make space for new entry by evicting old entries
   */
  private makeSpace(requiredSize: number): boolean {
    if (requiredSize > this.config.maxSize) {
      return false; // Entry too large for cache
    }

    const entries = Array.from(this.cache.entries());
    
    // Sort by priority and last accessed time
    entries.sort(([, a], [, b]) => {
      const priorityOrder = { low: 0, normal: 1, high: 2 };
      const aPriority = priorityOrder[a.metadata?.priority as keyof typeof priorityOrder] ?? 1;
      const bPriority = priorityOrder[b.metadata?.priority as keyof typeof priorityOrder] ?? 1;
      
      if (aPriority !== bPriority) {
        return aPriority - bPriority; // Lower priority first
      }
      
      return a.lastAccessed.getTime() - b.lastAccessed.getTime(); // Older first
    });

    let freedSpace = 0;
    let index = 0;

    while (
      freedSpace < requiredSize &&
      index < entries.length &&
      this.metrics.totalSize + requiredSize - freedSpace > this.config.maxSize
    ) {
      const [key, entry] = entries[index];
      this.cache.delete(key);
      freedSpace += entry.size;
      this.metrics.evictionCount++;
      index++;
    }

    this.updateCacheSize();
    return freedSpace >= requiredSize;
  }

  /**
   * Compress value (placeholder implementation)
   */
  private compress<T>(value: T): T {
    // In a real implementation, you might use a compression library
    // For now, just return the value as-is
    return value;
  }

  /**
   * Update cache size metrics
   */
  private updateCacheSize(): void {
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += entry.size;
    }
    
    this.metrics.totalEntries = this.cache.size;
    this.metrics.totalSize = totalSize;
    this.metrics.memoryUsage = {
      used: totalSize,
      available: this.config.maxSize - totalSize,
      percentage: (totalSize / this.config.maxSize) * 100,
    };
  }

  /**
   * Update hit/miss metrics
   */
  private updateMetrics(type: 'hit' | 'miss'): void {
    if (!this.config.enableMetrics) return;
    
    if (type === 'hit') {
      this.metrics.hitCount++;
    } else {
      this.metrics.missCount++;
    }
    
    const total = this.metrics.hitCount + this.metrics.missCount;
    this.metrics.hitRate = total > 0 ? (this.metrics.hitCount / total) * 100 : 0;
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = new Date();
    const expiredKeys: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt && entry.expiresAt < now) {
        expiredKeys.push(key);
      }
    }
    
    for (const key of expiredKeys) {
      this.cache.delete(key);
    }
    
    if (expiredKeys.length > 0) {
      this.updateCacheSize();
    }
  }

  /**
   * Destroy cache manager
   */
  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.clear();
  }
}

// Global cache instance
export const globalCache = new CacheManager({
  maxSize: 50 * 1024 * 1024, // 50MB
  maxEntries: 500,
  defaultTTL: 15 * 60 * 1000, // 15 minutes
  cleanupInterval: 2 * 60 * 1000, // 2 minutes
  enableCompression: false,
  enableMetrics: true,
});

// Cache key generators
export const CacheKeys = {
  projectStats: (projectId: string) => `project:${projectId}:stats`,
  sourceContent: (sourceId: string) => `source:${sourceId}:content`,
  sourceTracking: (projectId: string, sourceId: string) => `tracking:${projectId}:${sourceId}`,
  contentChunks: (projectId: string, contentHash: string) => `chunks:${projectId}:${contentHash}`,
  embeddings: (contentHash: string) => `embeddings:${contentHash}`,
  searchResults: (projectId: string, query: string) => `search:${projectId}:${btoa(query)}`,
};
