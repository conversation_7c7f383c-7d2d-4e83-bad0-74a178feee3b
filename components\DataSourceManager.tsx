import React, { useState } from 'react';
import { LinkIcon, DocumentTextIcon } from './Icons';
import { DragDropZone, FileWithPreview } from './DragDropZone';
import { useToast } from './Toast';
import { EnhancedErrorHandler } from '../utils/errorHandling';
import { processFile } from '../services/projectService';
import { SourceTrackingService } from '../services/sourceTrackingService';
import { fileProcessor } from '../services/fileProcessorService';
import { SourceEditor } from './SourceEditor';
import { SourceVersioningService } from '../services/sourceVersioningService';

export enum SourceType {
  URL = 'url',
  TEXT = 'text',
  FILE = 'file',
}

export interface DataSource {
  id: string;
  type: SourceType;
  reference: string;
  content: string;
  addedAt: Date;
}

export interface DataSourceManagerProps {
  dataSources: DataSource[];
  onDataSourcesChange: (sources: DataSource[]) => void;
  disabled?: boolean;
  className?: string;
  projectId?: string;
  enableIncrementalUpdates?: boolean;
  showSourceAnalysis?: boolean;
}

type InputType = 'url' | 'text' | 'file';

export const DataSourceManager: React.FC<DataSourceManagerProps> = ({
  dataSources,
  onDataSourcesChange,
  disabled = false,
  className = '',
  projectId,
  enableIncrementalUpdates = false,
  showSourceAnalysis = false,
}) => {
  const [inputType, setInputType] = useState<InputType>('url');
  const [urlInput, setUrlInput] = useState('');
  const [textInput, setTextInput] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<FileWithPreview[]>([]);
  const [isAddingSource, setIsAddingSource] = useState(false);
  const [sourceAnalysis, setSourceAnalysis] = useState<{
    newSources: number;
    modifiedSources: number;
    unchangedSources: number;
    orphanedSources: number;
  } | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [editingSource, setEditingSource] = useState<DataSource | null>(null);
  const { addToast } = useToast();

  const handleEditSource = (source: DataSource) => {
    setEditingSource(source);
  };

  const handleSaveSource = async (updatedSource: DataSource) => {
    try {
      // Create a version for the change
      await SourceVersioningService.createVersion(
        updatedSource,
        'updated',
        'Source edited via UI',
        'user', // In a real app, this would be the current user
        'Manual edit'
      );

      // Update the source in the list
      const updatedSources = dataSources.map(source =>
        source.id === updatedSource.id ? updatedSource : source
      );

      onDataSourcesChange(updatedSources);
      setEditingSource(null);

      addToast({
        type: 'success',
        title: 'Source Updated',
        message: 'Source has been successfully updated and versioned',
      });
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Update Failed',
        message: error instanceof Error ? error.message : 'Failed to update source',
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingSource(null);
  };

  const addDataSource = async () => {
    if (isAddingSource || disabled) return;

    setIsAddingSource(true);
    try {
      let newSource: DataSource;

      switch (inputType) {
        case 'url':
          if (!urlInput.trim()) {
            throw new Error('Please enter a valid URL');
          }

          // Enhanced URL validation
          if (!urlInput.match(/^https?:\/\/.+/)) {
            throw new Error('URL must start with http:// or https://');
          }

          const urlContent = await EnhancedErrorHandler.withRetry(
            async () => {
              const response = await fetch(urlInput);
              if (!response.ok) {
                throw new Error(`Failed to fetch URL: ${response.status} ${response.statusText}`);
              }
              return response.text();
            },
            { maxAttempts: 3 },
            (attempt) => {
              addToast({
                type: 'info',
                title: 'Retrying URL Fetch',
                message: `Retrying URL fetch (attempt ${attempt + 1})`,
              });
            }
          );

          newSource = {
            id: `url-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type: SourceType.URL,
            reference: urlInput,
            content: urlContent,
            addedAt: new Date(),
          };
          setUrlInput('');
          break;

        case 'text':
          if (!textInput.trim()) {
            throw new Error('Please enter some text content');
          }

          if (textInput.length < 10) {
            throw new Error('Text content must be at least 10 characters long');
          }

          newSource = {
            id: `text-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type: SourceType.TEXT,
            reference: `Text content (${textInput.length} chars)`,
            content: textInput,
            addedAt: new Date(),
          };
          setTextInput('');
          break;

        case 'file':
          if (!selectedFile) {
            throw new Error('Please select a file');
          }

          const fileContent = await EnhancedErrorHandler.withRetry(
            () => processFile(selectedFile),
            { maxAttempts: 2 }
          );

          newSource = {
            id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type: SourceType.FILE,
            reference: selectedFile.name,
            content: fileContent,
            addedAt: new Date(),
          };
          setSelectedFile(null);
          break;

        default:
          throw new Error('Invalid input type');
      }

      const updatedSources = [...dataSources, newSource];
      onDataSourcesChange(updatedSources);

      addToast({
        type: 'success',
        title: 'Source Added',
        message: `${newSource.type.toUpperCase()} source added successfully`,
      });

    } catch (error) {
      const errorDetails = EnhancedErrorHandler.getKnowledgeBaseErrorMessage(
        error instanceof Error ? error : new Error(String(error)),
        'add_source'
      );

      addToast({
        type: errorDetails.type,
        title: errorDetails.title,
        message: errorDetails.message,
        action: errorDetails.action,
      });
    } finally {
      setIsAddingSource(false);
    }
  };

  const removeDataSource = (id: string) => {
    const updatedSources = dataSources.filter(source => source.id !== id);
    onDataSourcesChange(updatedSources);
    
    addToast({
      type: 'info',
      title: 'Source Removed',
      message: 'Data source has been removed',
    });
  };

  const handleFilesAdded = async (filesWithPreview: FileWithPreview[]) => {
    setSelectedFiles(prev => [...prev, ...filesWithPreview]);
    
    const validFiles = filesWithPreview.filter(f => !f.error);
    const newSources: DataSource[] = [];

    for (const fileWithPreview of validFiles) {
      try {
        const fileContent = await EnhancedErrorHandler.withRetry(
          () => processFile(fileWithPreview.file),
          { maxAttempts: 2 }
        );
        
        const newSource: DataSource = {
          id: fileWithPreview.id,
          type: SourceType.FILE,
          reference: fileWithPreview.file.name,
          content: fileContent,
          addedAt: new Date(),
        };
        
        newSources.push(newSource);
      } catch (error) {
        const errorDetails = EnhancedErrorHandler.getKnowledgeBaseErrorMessage(
          error instanceof Error ? error : new Error(String(error)),
          'add_source'
        );
        
        addToast({
          type: errorDetails.type,
          title: `Failed to process ${fileWithPreview.file.name}`,
          message: errorDetails.message,
        });
      }
    }

    if (newSources.length > 0) {
      const updatedSources = [...dataSources, ...newSources];
      onDataSourcesChange(updatedSources);
      
      addToast({
        type: 'success',
        title: 'Files Processed',
        message: `${newSources.length} file(s) processed successfully`,
      });
    }
  };

  const handleFileRemoved = (fileId: string) => {
    setSelectedFiles(prev => prev.filter(f => f.id !== fileId));
    // Also remove from data sources if it was successfully processed
    const updatedSources = dataSources.filter(source => source.id !== fileId);
    onDataSourcesChange(updatedSources);
  };

  const analyzeIncrementalUpdates = async () => {
    if (!projectId || !enableIncrementalUpdates) return;

    // Check if source tracking is available
    if (!SourceTrackingService.isSourceTrackingAvailable()) {
      addToast({
        type: 'info',
        title: 'Source Tracking Unavailable',
        message: 'Please log in to enable source change tracking. All sources will be processed as new.',
        duration: 6000,
      });

      // Set fallback analysis
      setSourceAnalysis({
        newSources: dataSources.length,
        modifiedSources: 0,
        unchangedSources: 0,
        orphanedSources: 0,
      });
      return;
    }

    setIsAnalyzing(true);
    try {
      const analysis = await SourceTrackingService.analyzeIncrementalUpdate(projectId, dataSources);

      setSourceAnalysis({
        newSources: analysis.newSources.length,
        modifiedSources: analysis.modifiedSources.length,
        unchangedSources: analysis.unchangedSources.length,
        orphanedSources: analysis.orphanedSources.length,
      });

      // Show recommendations if available
      if (analysis.recommendations && analysis.recommendations.length > 0) {
        addToast({
          type: 'info',
          title: 'Source Analysis Complete',
          message: analysis.recommendations.join('. '),
          duration: 6000,
        });
      } else {
        addToast({
          type: 'info',
          title: 'Source Analysis Complete',
          message: `Found ${analysis.newSources.length} new, ${analysis.modifiedSources.length} modified, ${analysis.unchangedSources.length} unchanged sources`,
          duration: 5000,
        });
      }
    } catch (error) {
      console.error('Error analyzing incremental updates:', error);

      // Check if it's a permissions error
      if (error instanceof Error && error.message.includes('permissions')) {
        addToast({
          type: 'warning',
          title: 'Analysis Unavailable',
          message: 'Source tracking requires updated permissions. All sources will be processed.',
          duration: 8000,
        });

        // Set a fallback analysis where all sources are considered new
        setSourceAnalysis({
          newSources: dataSources.length,
          modifiedSources: 0,
          unchangedSources: 0,
          orphanedSources: 0,
        });
      } else {
        addToast({
          type: 'error',
          title: 'Analysis Failed',
          message: 'Failed to analyze source changes',
        });
      }
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Auto-analyze when sources change and incremental updates are enabled
  React.useEffect(() => {
    if (enableIncrementalUpdates && projectId && dataSources.length > 0) {
      const timeoutId = setTimeout(analyzeIncrementalUpdates, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [dataSources, enableIncrementalUpdates, projectId]);

  const getTabClasses = (type: InputType) => {
    const baseClasses = "px-4 py-2 rounded-lg font-medium transition-colors";
    const activeClasses = "bg-primary text-white";
    const inactiveClasses = "bg-secondary-light dark:bg-slate-700 text-text-light dark:text-slate-200 hover:bg-gray-200 dark:hover:bg-slate-600";
    
    return `${baseClasses} ${inputType === type ? activeClasses : inactiveClasses}`;
  };

  return (
    <div className={`bg-panel-light dark:bg-slate-800 rounded-xl shadow-lg p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-text-light dark:text-slate-200 mb-4">
        Add Content Sources
      </h3>

      {/* Input Type Tabs */}
      <div className="flex gap-2 mb-6">
        <button
          onClick={() => setInputType('url')}
          disabled={disabled}
          className={getTabClasses('url')}
        >
          <LinkIcon className="w-4 h-4 inline mr-2" />
          URL
        </button>
        <button
          onClick={() => setInputType('text')}
          disabled={disabled}
          className={getTabClasses('text')}
        >
          <DocumentTextIcon className="w-4 h-4 inline mr-2" />
          Text
        </button>
        <button
          onClick={() => setInputType('file')}
          disabled={disabled}
          className={getTabClasses('file')}
        >
          <svg className="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
          File
        </button>
      </div>

      {/* URL Input */}
      {inputType === 'url' && (
        <div className="flex gap-3 mb-4">
          <input
            type="url"
            value={urlInput}
            onChange={(e) => setUrlInput(e.target.value)}
            placeholder="https://example.com/faq"
            className="flex-1 px-3 py-2 border border-border-light dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none bg-white dark:bg-slate-700 text-text-light dark:text-slate-200"
            disabled={disabled || isAddingSource}
          />
          <button
            onClick={addDataSource}
            disabled={!urlInput.trim() || disabled || isAddingSource}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isAddingSource ? 'Adding...' : 'Add URL'}
          </button>
        </div>
      )}

      {/* Text Input */}
      {inputType === 'text' && (
        <div className="mb-4">
          <textarea
            value={textInput}
            onChange={(e) => setTextInput(e.target.value)}
            placeholder="Paste your content here..."
            rows={6}
            className="w-full px-3 py-2 border border-border-light dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-primary focus:outline-none bg-white dark:bg-slate-700 text-text-light dark:text-slate-200"
            disabled={disabled || isAddingSource}
          />
          <div className="flex justify-between items-center mt-2">
            <span className="text-sm text-text-secondary-light dark:text-slate-400">
              {textInput.length} characters
            </span>
            <button
              onClick={addDataSource}
              disabled={!textInput.trim() || disabled || isAddingSource}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isAddingSource ? 'Adding...' : 'Add Text'}
            </button>
          </div>
        </div>
      )}

      {/* Enhanced File Input with Drag & Drop */}
      {inputType === 'file' && (
        <div className="mb-4">
          <DragDropZone
            onFilesAdded={handleFilesAdded}
            onFileRemoved={handleFileRemoved}
            acceptedTypes={['.txt', '.md', 'text/plain', 'text/markdown']}
            maxFileSize={10 * 1024 * 1024} // 10MB
            maxFiles={10}
            disabled={disabled}
            showPreview={true}
            multiple={true}
            className="mb-4"
          />
          
          {/* Legacy single file input for backward compatibility */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
            <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Single File Upload (Legacy)
            </h5>
            <div className="flex items-center gap-3">
              <input
                type="file"
                accept=".txt,.md"
                onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                className="text-sm text-gray-600 dark:text-gray-400"
                disabled={disabled || isAddingSource}
              />
              {selectedFile && (
                <button
                  onClick={addDataSource}
                  disabled={!selectedFile || disabled || isAddingSource}
                  className="px-3 py-1 text-sm bg-primary text-white rounded hover:bg-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isAddingSource ? 'Processing...' : 'Add File'}
                </button>
              )}
            </div>
            {selectedFile && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
              </p>
            )}
          </div>
        </div>
      )}

      {/* Incremental Update Analysis */}
      {enableIncrementalUpdates && showSourceAnalysis && (
        <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <h5 className="text-sm font-medium text-blue-800 dark:text-blue-200">
              📊 Source Change Analysis
            </h5>
            <button
              onClick={analyzeIncrementalUpdates}
              disabled={isAnalyzing || disabled}
              className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {isAnalyzing ? 'Analyzing...' : 'Refresh Analysis'}
            </button>
          </div>

          {sourceAnalysis ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
              <div className="text-center">
                <div className="text-lg font-bold text-green-600 dark:text-green-400">
                  {sourceAnalysis.newSources}
                </div>
                <div className="text-blue-700 dark:text-blue-300">New</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
                  {sourceAnalysis.modifiedSources}
                </div>
                <div className="text-blue-700 dark:text-blue-300">Modified</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-600 dark:text-gray-400">
                  {sourceAnalysis.unchangedSources}
                </div>
                <div className="text-blue-700 dark:text-blue-300">Unchanged</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-red-600 dark:text-red-400">
                  {sourceAnalysis.orphanedSources}
                </div>
                <div className="text-blue-700 dark:text-blue-300">Orphaned</div>
              </div>
            </div>
          ) : (
            <div className="text-sm text-blue-700 dark:text-blue-300">
              {isAnalyzing ? 'Analyzing source changes...' : 'Click "Refresh Analysis" to check for changes'}
            </div>
          )}
        </div>
      )}

      {/* Source Editor */}
      {editingSource && (
        <SourceEditor
          source={editingSource}
          onSave={handleSaveSource}
          onCancel={handleCancelEdit}
          disabled={disabled}
        />
      )}

      {/* Data Sources List */}
      {dataSources.length > 0 && !editingSource && (
        <DataSourceList
          dataSources={dataSources}
          onRemove={removeDataSource}
          onEdit={handleEditSource}
          disabled={disabled}
          enableIncrementalUpdates={enableIncrementalUpdates}
          projectId={projectId}
        />
      )}
    </div>
  );
};

interface DataSourceListProps {
  dataSources: DataSource[];
  onRemove: (id: string) => void;
  onEdit?: (source: DataSource) => void;
  disabled?: boolean;
  enableIncrementalUpdates?: boolean;
  projectId?: string;
}

const DataSourceList: React.FC<DataSourceListProps> = ({
  dataSources,
  onRemove,
  onEdit,
  disabled = false,
  enableIncrementalUpdates = false,
  projectId,
}) => {
  const [sourceStatuses, setSourceStatuses] = useState<Record<string, 'new' | 'modified' | 'unchanged'>>({});

  // Check source statuses if incremental updates are enabled
  React.useEffect(() => {
    if (!enableIncrementalUpdates || !projectId) return;

    const checkSourceStatuses = async () => {
      const statuses: Record<string, 'new' | 'modified' | 'unchanged'> = {};

      for (const source of dataSources) {
        try {
          const changeDetection = await SourceTrackingService.detectSourceChanges(projectId, source);
          statuses[source.id] = changeDetection.changeType === 'new' ? 'new' :
                                changeDetection.changeType === 'modified' ? 'modified' : 'unchanged';
        } catch (error) {
          console.error(`Error checking status for source ${source.id}:`, error);
          // Default to 'new' if we can't check status due to permissions
          statuses[source.id] = 'new';
        }
      }

      setSourceStatuses(statuses);
    };

    checkSourceStatuses();
  }, [dataSources, enableIncrementalUpdates, projectId]);

  const getStatusIndicator = (sourceId: string) => {
    if (!enableIncrementalUpdates) return null;

    const status = sourceStatuses[sourceId];
    if (!status) return null;

    switch (status) {
      case 'new':
        return <span className="inline-block w-2 h-2 bg-green-500 rounded-full" title="New source" />;
      case 'modified':
        return <span className="inline-block w-2 h-2 bg-orange-500 rounded-full" title="Modified source" />;
      case 'unchanged':
        return <span className="inline-block w-2 h-2 bg-gray-400 rounded-full" title="Unchanged source" />;
      default:
        return null;
    }
  };
  return (
    <div className="space-y-2">
      <h4 className="font-medium text-text-light dark:text-slate-200">
        Added Sources ({dataSources.length}):
      </h4>
      {dataSources.map((source) => (
        <div
          key={source.id}
          className="flex items-center justify-between p-3 bg-secondary-light dark:bg-slate-700 rounded-lg"
        >
          <div className="flex items-center gap-3">
            {source.type === SourceType.URL ? (
              <LinkIcon className="w-4 h-4 text-primary" />
            ) : source.type === SourceType.FILE ? (
              <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            ) : (
              <DocumentTextIcon className="w-4 h-4 text-primary" />
            )}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <span className="text-sm text-text-light dark:text-slate-200 truncate">
                  {source.reference}
                </span>
                {getStatusIndicator(source.id)}
              </div>
              <span className="text-xs text-text-secondary-light dark:text-slate-400">
                Added {source.addedAt.toLocaleString()}
              </span>
            </div>
          </div>
          <div className="flex gap-2">
            {onEdit && (
              <button
                onClick={() => onEdit(source)}
                disabled={disabled}
                className="text-blue-500 hover:text-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Edit source"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
            )}
            <button
              onClick={() => onRemove(source.id)}
              disabled={disabled}
              className="text-red-500 hover:text-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Remove source"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};
