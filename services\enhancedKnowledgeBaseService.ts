/**
 * Enhanced Knowledge Base Service with proper error handling, logging, and validation
 */

import { BaseService, ServiceResponse, ValidationSchema, CommonValidationRules } from './baseService';
import { DataSource, SourceType, KnowledgeBaseStats } from '../types/knowledgeBase';
import { 
  processAndStoreContent,
  getProjectKnowledgeBaseStats,
  clearProjectKnowledgeBase,
  processFile
} from './projectService';
import { SourceTrackingService } from './sourceTrackingService';
import { ContentOptimizer } from '../utils/contentOptimization';
import { globalCache, CacheKeys } from '../utils/cacheManager';

export interface BuildKnowledgeBaseRequest {
  projectId: string;
  sources: DataSource[];
  options?: {
    enableDeduplication?: boolean;
    maxChunkSize?: number;
    chunkOverlap?: number;
    batchSize?: number;
    forceRebuild?: boolean;
  };
}

export interface BuildKnowledgeBaseResponse {
  stats: KnowledgeBaseStats;
  processedSources: number;
  totalChunks: number;
  processingTime: number;
  optimizationResult?: {
    originalChunks: number;
    optimizedChunks: number;
    duplicatesRemoved: number;
    sizeReduction: number;
  };
}

export interface ClearKnowledgeBaseRequest {
  projectId: string;
  confirmationToken?: string;
}

export interface ProcessSourceRequest {
  projectId: string;
  source: DataSource;
  options?: {
    validateContent?: boolean;
    trackSource?: boolean;
  };
}

export interface ProcessSourceResponse {
  sourceId: string;
  contentLength: number;
  processingTime: number;
  chunkIds: string[];
  warnings?: string[];
}

export class EnhancedKnowledgeBaseService extends BaseService {
  constructor() {
    super('KnowledgeBaseService', {
      retryAttempts: 3,
      retryDelay: 1000,
      timeout: 60000, // 60 seconds for knowledge base operations
      enableLogging: true,
      logLevel: 'info',
    });
  }

  /**
   * Build knowledge base with enhanced error handling and optimization
   */
  public async buildKnowledgeBase(
    request: BuildKnowledgeBaseRequest
  ): Promise<ServiceResponse<BuildKnowledgeBaseResponse>> {
    // Validate input
    const validation = this.validateBuildRequest(request);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: `Validation failed: ${validation.errors.join(', ')}`,
          retryable: false,
          category: 'validation',
          details: { errors: validation.errors, warnings: validation.warnings },
        },
      };
    }

    return this.executeWithRetry(
      async () => {
        const startTime = Date.now();
        const { projectId, sources, options = {} } = request;

        // Check cache for recent build results
        const cacheKey = CacheKeys.projectStats(projectId);
        const cachedStats = globalCache.get<KnowledgeBaseStats>(cacheKey);

        // Analyze incremental updates if not forcing rebuild
        let sourcesToProcess = sources;
        if (!options.forceRebuild) {
          const analysis = await SourceTrackingService.analyzeIncrementalUpdate(projectId, sources);
          sourcesToProcess = [...analysis.newSources, ...analysis.modifiedSources];

          this.log({
            level: 'info',
            message: `Incremental analysis: ${analysis.newSources.length} new, ${analysis.modifiedSources.length} modified, ${analysis.unchangedSources.length} unchanged`,
            metadata: { projectId, method: 'buildKnowledgeBase' },
          });

          // If no sources to process and we have cached stats, return cached result
          if (sourcesToProcess.length === 0 && cachedStats) {
            this.log({
              level: 'info',
              message: 'No changes detected, returning cached stats',
              metadata: { projectId, method: 'buildKnowledgeBase' },
            });

            return {
              stats: cachedStats,
              processedSources: 0,
              totalChunks: cachedStats.chunkCount,
              processingTime: Date.now() - startTime,
            };
          }
        }

        if (sourcesToProcess.length === 0) {
          const stats = await getProjectKnowledgeBaseStats(projectId);
          return {
            stats,
            processedSources: 0,
            totalChunks: stats.chunkCount,
            processingTime: Date.now() - startTime,
          };
        }

        // Process sources in parallel with caching
        const sourceProcessingPromises = sourcesToProcess.map(async (source) => {
          try {
            let content = source.content || '';

            // Check cache for URL content
            if (source.type === SourceType.URL && !content) {
              const contentCacheKey = CacheKeys.sourceContent(source.id);
              const cachedContent = globalCache.get<string>(contentCacheKey);

              if (cachedContent) {
                content = cachedContent;
                this.log({
                  level: 'debug',
                  message: `Using cached content for URL: ${source.reference}`,
                  metadata: { projectId, sourceId: source.id, method: 'buildKnowledgeBase' },
                });
              } else {
                const response = await fetch(source.reference);
                if (!response.ok) {
                  throw new Error(`Failed to fetch URL: ${response.status} ${response.statusText}`);
                }
                content = await response.text();

                // Cache the fetched content
                globalCache.set(contentCacheKey, content, {
                  ttl: 30 * 60 * 1000, // 30 minutes
                  tags: ['url-content', `project:${projectId}`],
                });
              }
            }

            return {
              success: true,
              source: { ...source, content },
              content,
            };
          } catch (error) {
            this.log({
              level: 'warn',
              message: `Failed to process source: ${source.reference}`,
              metadata: { projectId, sourceId: source.id, method: 'buildKnowledgeBase' },
              error: error instanceof Error ? error : new Error(String(error)),
            });

            return {
              success: false,
              source,
              error: error instanceof Error ? error : new Error(String(error)),
            };
          }
        });

        const sourceResults = await Promise.allSettled(sourceProcessingPromises);
        const successfulSources = sourceResults
          .filter((result): result is PromiseFulfilledResult<any> => 
            result.status === 'fulfilled' && result.value.success
          )
          .map(result => result.value);

        if (successfulSources.length === 0) {
          throw new Error('No sources could be processed successfully');
        }

        // Optimize content
        const sourcesForOptimization = successfulSources.map(item => ({
          sourceId: item.source.id,
          sourceType: item.source.type,
          content: item.content,
        }));

        const optimizationResult = ContentOptimizer.optimizeContent(sourcesForOptimization, {
          enableDeduplication: options.enableDeduplication ?? true,
          maxChunkSize: options.maxChunkSize ?? 4000,
          chunkOverlap: options.chunkOverlap ?? 200,
        });

        // Process content in batches
        const batchSize = options.batchSize ?? 5;
        const allContent = successfulSources.map(item => item.content).join('\n\n');
        
        // Store content (using existing service)
        const primarySource = successfulSources[0].source;
        await processAndStoreContent(
          projectId,
          allContent,
          primarySource.type,
          primarySource.reference
        );

        // Track processed sources
        const trackingPromises = successfulSources.map(async (item) => {
          const chunkIds = optimizationResult.chunks
            .filter(chunk => chunk.sourceId === item.source.id)
            .map(chunk => chunk.id);
          
          await SourceTrackingService.trackSource(
            projectId,
            item.source,
            chunkIds,
            optimizationResult.result.processingTime
          );
        });

        await Promise.all(trackingPromises);

        // Get updated stats and cache them
        const stats = await getProjectKnowledgeBaseStats(projectId);
        const processingTime = Date.now() - startTime;

        // Cache the updated stats
        globalCache.set(CacheKeys.projectStats(projectId), stats, {
          ttl: 10 * 60 * 1000, // 10 minutes
          tags: ['project-stats', `project:${projectId}`],
        });

        // Clear related caches
        globalCache.clearByTags([`project:${projectId}`]);

        this.log({
          level: 'info',
          message: `Knowledge base built successfully: ${successfulSources.length} sources, ${optimizationResult.chunks.length} chunks`,
          metadata: { projectId, method: 'buildKnowledgeBase' },
          duration: processingTime,
        });

        return {
          stats,
          processedSources: successfulSources.length,
          totalChunks: optimizationResult.chunks.length,
          processingTime,
          optimizationResult: {
            originalChunks: optimizationResult.result.originalChunks,
            optimizedChunks: optimizationResult.result.optimizedChunks,
            duplicatesRemoved: optimizationResult.result.duplicatesRemoved,
            sizeReduction: optimizationResult.result.totalSizeReduction,
          },
        };
      },
      {
        method: 'buildKnowledgeBase',
        projectId: request.projectId,
        params: { sourcesCount: request.sources.length },
      }
    );
  }

  /**
   * Clear knowledge base with confirmation
   */
  public async clearKnowledgeBase(
    request: ClearKnowledgeBaseRequest
  ): Promise<ServiceResponse<{ success: boolean; clearedChunks: number }>> {
    const validation = this.validateClearRequest(request);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: `Validation failed: ${validation.errors.join(', ')}`,
          retryable: false,
          category: 'validation',
        },
      };
    }

    return this.executeWithRetry(
      async () => {
        const { projectId } = request;
        
        // Get current stats before clearing
        const currentStats = await getProjectKnowledgeBaseStats(projectId);
        const clearedChunks = currentStats.chunkCount;

        // Clear the knowledge base
        await clearProjectKnowledgeBase(projectId);

        // Clean up source tracking
        const trackedSources = await SourceTrackingService.getProjectSources(projectId);
        await SourceTrackingService.cleanupOrphanedSources(projectId, trackedSources);

        this.log({
          level: 'info',
          message: `Knowledge base cleared: ${clearedChunks} chunks removed`,
          metadata: { projectId, method: 'clearKnowledgeBase' },
        });

        return { success: true, clearedChunks };
      },
      {
        method: 'clearKnowledgeBase',
        projectId: request.projectId,
      }
    );
  }

  /**
   * Process a single source
   */
  public async processSource(
    request: ProcessSourceRequest
  ): Promise<ServiceResponse<ProcessSourceResponse>> {
    const validation = this.validateProcessSourceRequest(request);
    if (!validation.isValid) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: `Validation failed: ${validation.errors.join(', ')}`,
          retryable: false,
          category: 'validation',
        },
      };
    }

    return this.executeWithRetry(
      async () => {
        const startTime = Date.now();
        const { projectId, source, options = {} } = request;

        let content = source.content || '';
        const warnings: string[] = [];

        // Process based on source type
        if (source.type === SourceType.FILE && !content) {
          throw new Error('File content is required for file sources');
        } else if (source.type === SourceType.URL && !content) {
          const response = await fetch(source.reference);
          if (!response.ok) {
            throw new Error(`Failed to fetch URL: ${response.status} ${response.statusText}`);
          }
          content = await response.text();
        }

        // Validate content if requested
        if (options.validateContent) {
          const contentValidation = ContentOptimizer.validateContent(content);
          if (!contentValidation.isValid) {
            throw new Error(`Content validation failed: ${contentValidation.errors.join(', ')}`);
          }
          warnings.push(...contentValidation.warnings);
        }

        // Generate mock chunk IDs (in real implementation, this would come from actual processing)
        const estimatedChunks = Math.ceil(content.length / 4000);
        const chunkIds = Array.from({ length: estimatedChunks }, (_, i) => 
          `${source.id}-chunk-${i}`
        );

        // Track source if requested
        if (options.trackSource) {
          await SourceTrackingService.trackSource(
            projectId,
            source,
            chunkIds,
            Date.now() - startTime
          );
        }

        const processingTime = Date.now() - startTime;

        return {
          sourceId: source.id,
          contentLength: content.length,
          processingTime,
          chunkIds,
          warnings: warnings.length > 0 ? warnings : undefined,
        };
      },
      {
        method: 'processSource',
        projectId: request.projectId,
        params: { sourceId: request.source.id, sourceType: request.source.type },
      }
    );
  }

  /**
   * Validate build knowledge base request
   */
  private validateBuildRequest(request: BuildKnowledgeBaseRequest) {
    const schema: ValidationSchema = {
      rules: [
        CommonValidationRules.required('projectId'),
        {
          name: 'sources_array',
          validate: (req: BuildKnowledgeBaseRequest) => Array.isArray(req.sources) && req.sources.length > 0,
          message: 'At least one source is required',
          severity: 'error',
        },
        {
          name: 'sources_valid',
          validate: (req: BuildKnowledgeBaseRequest) => req.sources.every(source => 
            source.id && source.type && source.reference
          ),
          message: 'All sources must have id, type, and reference',
          severity: 'error',
        },
      ],
    };

    return this.validateInput(request, schema);
  }

  /**
   * Validate clear knowledge base request
   */
  private validateClearRequest(request: ClearKnowledgeBaseRequest) {
    const schema: ValidationSchema = {
      rules: [
        CommonValidationRules.required('projectId'),
      ],
    };

    return this.validateInput(request, schema);
  }

  /**
   * Validate process source request
   */
  private validateProcessSourceRequest(request: ProcessSourceRequest) {
    const schema: ValidationSchema = {
      rules: [
        CommonValidationRules.required('projectId'),
        {
          name: 'source_object',
          validate: (req: ProcessSourceRequest) => req.source && typeof req.source === 'object',
          message: 'Source object is required',
          severity: 'error',
        },
        {
          name: 'source_properties',
          validate: (req: ProcessSourceRequest) => 
            req.source.id && req.source.type && req.source.reference,
          message: 'Source must have id, type, and reference',
          severity: 'error',
        },
      ],
    };

    return this.validateInput(request, schema);
  }

  /**
   * Perform health checks for the knowledge base service
   */
  protected async performHealthChecks() {
    const checks = [];

    // Check if we can access the database
    try {
      const startTime = Date.now();
      // This would be a simple database query in a real implementation
      await new Promise(resolve => setTimeout(resolve, 100));
      checks.push({
        name: 'database_connection',
        status: 'pass' as const,
        duration: Date.now() - startTime,
      });
    } catch (error) {
      checks.push({
        name: 'database_connection',
        status: 'fail' as const,
        message: error instanceof Error ? error.message : 'Database connection failed',
      });
    }

    // Check content optimization service
    try {
      const startTime = Date.now();
      ContentOptimizer.validateContent('test content');
      checks.push({
        name: 'content_optimizer',
        status: 'pass' as const,
        duration: Date.now() - startTime,
      });
    } catch (error) {
      checks.push({
        name: 'content_optimizer',
        status: 'fail' as const,
        message: error instanceof Error ? error.message : 'Content optimizer failed',
      });
    }

    return checks;
  }
}
