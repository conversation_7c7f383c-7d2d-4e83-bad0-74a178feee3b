rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can read and write projects they own
    // Public read access for chat functionality (projects and chunks)
    match /projects/{projectId} {
      allow read: if true; // Allow public read access for chat
      allow write: if request.auth != null && request.auth.uid == resource.data.ownerId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.ownerId;

      // Public read access for chunks (needed for chat), write access for owners only
      match /chunks/{chunkId} {
        allow read: if true; // Allow public read access for chat
        allow write: if request.auth != null &&
          request.auth.uid == get(/databases/$(database)/documents/projects/$(projectId)).data.ownerId;
      }

      // Source tracking for knowledge base management
      match /sources/{sourceId} {
        allow read, write: if request.auth != null &&
          request.auth.uid == get(/databases/$(database)/documents/projects/$(projectId)).data.ownerId;
      }
    }

    // Global source tracking collection (alternative approach)
    match /sourceTracking/{trackingId} {
      allow read: if request.auth != null && (
        resource == null ||
        request.auth.uid == resource.data.ownerId
      );
      allow write: if request.auth != null &&
        request.auth.uid == resource.data.ownerId;
      allow create: if request.auth != null &&
        request.auth.uid == request.resource.data.ownerId;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}