/**
 * Tests for enhanced error handling and user feedback improvements
 */

import { describe, it, expect, vi } from 'vitest';
import { EnhancedErrorHandler } from '../utils/errorHandling';

describe('Enhanced Error Handling', () => {
  describe('Error Analysis', () => {
    it('should identify network errors correctly', () => {
      const networkError = new Error('Failed to fetch');
      const result = EnhancedErrorHandler.analyzeError(networkError);
      
      expect(result.category).toBe('network');
      expect(result.type).toBe('error');
      expect(result.title).toBe('Connection Problem');
      expect(result.retryable).toBe(true);
    });

    it('should identify validation errors correctly', () => {
      const validationError = new Error('Invalid URL format');
      const result = EnhancedErrorHandler.analyzeError(validationError);
      
      expect(result.category).toBe('validation');
      expect(result.type).toBe('error');
      expect(result.retryable).toBe(false);
    });

    it('should identify permission errors correctly', () => {
      const permissionError = new Error('Permission denied');
      const result = EnhancedErrorHandler.analyzeError(permissionError);
      
      expect(result.category).toBe('permission');
      expect(result.type).toBe('error');
      expect(result.retryable).toBe(false);
    });

    it('should identify processing errors correctly', () => {
      const processingError = new Error('Failed to process chunks');
      const result = EnhancedErrorHandler.analyzeError(processingError);
      
      expect(result.category).toBe('processing');
      expect(result.type).toBe('error');
      expect(result.retryable).toBe(true);
    });

    it('should handle unknown errors gracefully', () => {
      const unknownError = new Error('Something weird happened');
      const result = EnhancedErrorHandler.analyzeError(unknownError);
      
      expect(result.category).toBe('unknown');
      expect(result.type).toBe('error');
      expect(result.retryable).toBe(true);
    });
  });

  describe('Knowledge Base Error Messages', () => {
    it('should provide context-specific error messages for add_source', () => {
      const error = new Error('Invalid URL format');
      const result = EnhancedErrorHandler.getKnowledgeBaseErrorMessage(error, 'add_source');
      
      expect(result.title).toContain('Failed to Add');
      expect(result.title).toContain('Invalid');
    });

    it('should provide context-specific error messages for build_kb', () => {
      const error = new Error('Network error');
      const result = EnhancedErrorHandler.getKnowledgeBaseErrorMessage(error, 'build_kb');
      
      expect(result.title).toBe('Knowledge Base Build Failed');
      expect(result.action).toBeDefined();
    });

    it('should provide context-specific error messages for clear_kb', () => {
      const error = new Error('Database error');
      const result = EnhancedErrorHandler.getKnowledgeBaseErrorMessage(error, 'clear_kb');
      
      expect(result.title).toBe('Failed to Clear Knowledge Base');
    });
  });

  describe('Retry Logic', () => {
    it('should retry operations with exponential backoff', async () => {
      let attempts = 0;
      const mockOperation = vi.fn().mockImplementation(() => {
        attempts++;
        if (attempts < 3) {
          throw new Error('Temporary failure');
        }
        return 'success';
      });

      const result = await EnhancedErrorHandler.withRetry(
        mockOperation,
        { maxAttempts: 3, baseDelay: 10, maxDelay: 100, backoffFactor: 2 }
      );

      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(3);
    });

    it('should not retry non-retryable errors', async () => {
      const mockOperation = vi.fn().mockImplementation(() => {
        throw new Error('Invalid URL format');
      });

      await expect(
        EnhancedErrorHandler.withRetry(mockOperation, { maxAttempts: 3 })
      ).rejects.toThrow('Invalid URL format');

      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should call onRetry callback', async () => {
      let attempts = 0;
      const mockOperation = vi.fn().mockImplementation(() => {
        attempts++;
        if (attempts < 2) {
          throw new Error('Network timeout');
        }
        return 'success';
      });

      const onRetry = vi.fn();

      await EnhancedErrorHandler.withRetry(
        mockOperation,
        { maxAttempts: 3, baseDelay: 10 },
        onRetry
      );

      expect(onRetry).toHaveBeenCalledTimes(1);
      expect(onRetry).toHaveBeenCalledWith(1, expect.any(Error));
    });

    it('should respect maxAttempts limit', async () => {
      const mockOperation = vi.fn().mockImplementation(() => {
        throw new Error('Network timeout');
      });

      await expect(
        EnhancedErrorHandler.withRetry(mockOperation, { maxAttempts: 2, baseDelay: 10 })
      ).rejects.toThrow('Network timeout');

      expect(mockOperation).toHaveBeenCalledTimes(2);
    });
  });

  describe('Error Message Quality', () => {
    it('should provide actionable guidance for URL errors', () => {
      const error = new Error('Invalid URL');
      const result = EnhancedErrorHandler.analyzeError(error);
      
      expect(result.message).toContain('http://');
      expect(result.message).toContain('https://');
      expect(result.action).toBeDefined();
    });

    it('should provide helpful guidance for file type errors', () => {
      const error = new Error('Unsupported file type');
      const result = EnhancedErrorHandler.analyzeError(error);
      
      expect(result.message).toContain('.txt');
      expect(result.message).toContain('.md');
      expect(result.action).toBeDefined();
    });

    it('should provide troubleshooting tips for processing errors', () => {
      const error = new Error('Failed to process chunks');
      const result = EnhancedErrorHandler.analyzeError(error);
      
      expect(result.message).toContain('content');
      expect(result.retryable).toBe(true);
    });
  });
});
