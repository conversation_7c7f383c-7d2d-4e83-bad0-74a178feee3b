#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to deploy Firestore security rules
 * Run with: node deploy-firestore-rules.js
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🔥 Deploying Firestore Security Rules...\n');

// Check if Firebase CLI is installed
try {
  execSync('firebase --version', { stdio: 'pipe' });
  console.log('✅ Firebase CLI is installed');
} catch (error) {
  console.error('❌ Firebase CLI is not installed. Please install it first:');
  console.error('npm install -g firebase-tools');
  process.exit(1);
}

// Check if firebase.json exists
if (!fs.existsSync('firebase.json')) {
  console.error('❌ firebase.json not found. Please run "firebase init" first.');
  process.exit(1);
}

// Check if firestore.rules exists
if (!fs.existsSync('firestore.rules')) {
  console.error('❌ firestore.rules not found.');
  process.exit(1);
}

console.log('✅ Configuration files found');

// Check if user is logged in
try {
  const result = execSync('firebase projects:list', { encoding: 'utf8', stdio: 'pipe' });
  if (result.includes('ecomqna')) {
    console.log('✅ Authenticated and project found');
  } else {
    console.log('⚠️  Project "ecomqna" not found in your projects list');
  }
} catch (error) {
  console.error('❌ Not authenticated. Please run "firebase login" first.');
  process.exit(1);
}

// Deploy the rules
try {
  console.log('\n🚀 Deploying Firestore rules...');
  execSync('firebase deploy --only firestore:rules', { stdio: 'inherit' });
  console.log('\n✅ Firestore rules deployed successfully!');
  console.log('\n📝 Updated rules include:');
  console.log('   - User document access');
  console.log('   - Project and chunk access');
  console.log('   - Source tracking collections');
  console.log('\n🔄 Please restart your development server to ensure the changes take effect.');
} catch (error) {
  console.error('\n❌ Failed to deploy Firestore rules:', error.message);
  console.error('\n💡 Manual deployment steps:');
  console.error('1. Go to https://console.firebase.google.com/');
  console.error('2. Select your "ecomqna" project');
  console.error('3. Navigate to Firestore Database > Rules');
  console.error('4. Copy the rules from firestore.rules file');
  console.error('5. Click "Publish"');
  process.exit(1);
}
