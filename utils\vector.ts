
import { VectorData } from '../types';

export interface SimilarityResult {
  item: VectorData;
  score: number;
}

export interface SearchOptions {
  minSimilarity?: number;
  maxResults?: number;
  includeScores?: boolean;
  debug?: boolean;
}

export interface SearchResult {
  results: VectorData[];
  scores?: number[];
  debug?: {
    query: string;
    totalCandidates: number;
    aboveThreshold: number;
    topScore: number;
    averageScore: number;
  };
}

/**
 * Calculates the dot product of two vectors.
 * @param vecA - The first vector.
 * @param vecB - The second vector.
 * @returns The dot product.
 */
function dotProduct(vecA: number[], vecB: number[]): number {
  let product = 0;
  for (let i = 0; i < vecA.length; i++) {
    product += vecA[i] * vecB[i];
  }
  return product;
}

/**
 * Calculates the magnitude (or L2 norm) of a vector.
 * @param vec - The vector.
 * @returns The magnitude of the vector.
 */
function magnitude(vec: number[]): number {
  let sum = 0;
  for (let i = 0; i < vec.length; i++) {
    sum += vec[i] * vec[i];
  }
  return Math.sqrt(sum);
}

/**
 * Calculates the cosine similarity between two vectors.
 * @param vecA - The first vector.
 * @param vecB - The second vector.
 * @returns A similarity score between -1 and 1.
 */
function cosineSimilarity(vecA: number[], vecB: number[]): number {
  const dot = dotProduct(vecA, vecB);
  const magA = magnitude(vecA);
  const magB = magnitude(vecB);
  if (magA === 0 || magB === 0) {
    return 0; // Avoid division by zero
  }
  return dot / (magA * magB);
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use findSimilarVectors for better control and debugging
 */
export function findTopK(
  queryEmbedding: number[],
  vectorStore: VectorData[],
  k: number
): VectorData[] {
  const result = findSimilarVectors(queryEmbedding, vectorStore, {
    maxResults: k,
    minSimilarity: 0.0 // No threshold for backward compatibility
  });
  return result.results;
}

/**
 * Enhanced similarity search with thresholding and debugging capabilities.
 * @param queryEmbedding - The embedding of the user's query.
 * @param vectorStore - The array of stored vectors with their text.
 * @param options - Search options including similarity threshold and debugging.
 * @returns SearchResult with results, scores, and optional debug information.
 */
export function findSimilarVectors(
  queryEmbedding: number[],
  vectorStore: VectorData[],
  options: SearchOptions = {}
): SearchResult {
  const {
    minSimilarity = 0.25, // Default minimum similarity threshold
    maxResults = 5,
    includeScores = false,
    debug = false
  } = options;

  if (maxResults <= 0 || vectorStore.length === 0) {
    return {
      results: [],
      scores: includeScores ? [] : undefined,
      debug: debug ? {
        query: 'empty',
        totalCandidates: 0,
        aboveThreshold: 0,
        topScore: 0,
        averageScore: 0
      } : undefined
    };
  }

  // Calculate similarities for all vectors
  const similarities: SimilarityResult[] = vectorStore.map((item) => ({
    item,
    score: cosineSimilarity(queryEmbedding, item.embedding),
  }));

  // Sort by similarity score (highest first)
  similarities.sort((a, b) => b.score - a.score);

  // Filter by minimum similarity threshold
  const aboveThreshold = similarities.filter(sim => sim.score >= minSimilarity);

  // Take top results
  const topResults = aboveThreshold.slice(0, maxResults);

  // Prepare debug information
  let debugInfo;
  if (debug) {
    const scores = similarities.map(s => s.score);
    debugInfo = {
      query: `${queryEmbedding.length}d vector`,
      totalCandidates: vectorStore.length,
      aboveThreshold: aboveThreshold.length,
      topScore: scores.length > 0 ? Math.max(...scores) : 0,
      averageScore: scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0
    };

    console.log('Vector Search Debug:', {
      ...debugInfo,
      threshold: minSimilarity,
      returnedResults: topResults.length,
      topScores: topResults.map(r => r.score.toFixed(3))
    });
  }

  return {
    results: topResults.map(sim => sim.item),
    scores: includeScores ? topResults.map(sim => sim.score) : undefined,
    debug: debugInfo
  };
}
